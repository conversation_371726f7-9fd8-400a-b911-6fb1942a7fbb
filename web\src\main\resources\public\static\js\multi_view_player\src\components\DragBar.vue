<script setup lang="ts">
import { reactive, ref, useSlots, watch } from "vue";
import Iconfont from "@/components/Iconfont.vue";

const props = defineProps({
  barArr: {
    type: Array,
    default: () => [],
  },
});

const emits = defineEmits(["change"]);

const outerRef = reactive<any>([]);
const innerHeight = reactive<any>({});

watch(
  () => props.barArr,
  () => {
    props.barArr.map((el: any, i: number) => {
      if (el.defaultValue) {
        innerHeight[i] = el.defaultValue * 100;
      }
    });
  },
  {
    immediate: true,
    deep: true,
  }
);

const getOuterRef: any = (i: number) => {
  return (el: HTMLElement | null) => {
    if (el) {
      outerRef[i] = el;
    }
  };
};

const onMousedown = (e: any, bar: any, i: number) => {
  let height = outerRef[i]?.clientHeight;
  innerHeight[i] = ((height - e.offsetY) / height) * 100;
  let scale = (height - e.offsetY) / height;
  scale = scale < 0 ? 0 : scale;
  emits("change", { name: bar.name, value: scale });
  let startY = e.screenY;
  let startHeight = parseFloat(String(innerHeight[i])) / 100;
  document.onmousemove = function (e) {
    e.preventDefault();
    scale = (startY - e.screenY) / height + startHeight;
    scale = scale < 0 ? 0 : scale > 1 ? 1 : scale;
    innerHeight[i] = scale * 100;
    emits("change", { name: bar.name, value: scale });
  };
  document.onmouseup = function () {
    document.onmousemove = null;
    document.onmouseup = null;
  };
};

const onTouchstart = (e: any, bar: any, i: number) => {
  let height = outerRef[i]?.clientHeight;
  let scale = (height - (e.touches[0].clientY - e.target.getBoundingClientRect().top)) / height;
  scale = scale < 0 ? 0 : scale;
  innerHeight[i] = scale * 100;
  emits("change", { name: bar.name, value: scale });
  let startY = e.touches[0].clientY;
  let startHeight = parseFloat(String(innerHeight[i])) / 100;
  document.ontouchmove = function (e) {
    e.preventDefault();
    scale = (startY - e.touches[0].clientY) / height + startHeight;
    scale = scale < 0 ? 0 : scale > 1 ? 1 : scale;
    innerHeight[i] = scale * 100;
    emits("change", { name: bar.name, value: scale });
  };
  document.ontouchend = function (e) {
    document.ontouchmove = null;
    document.ontouchend = null;
  };
};
</script>

<template>
  <div class="drag_bar">
    <slot />
    <div class="bar_group">
      <div v-for="(item, i) in props.barArr" class="drag_container">
        <div class="outer" :ref="getOuterRef(i)">
          <div class="inner" :style="{ height: innerHeight[i] + '%' }"></div>
          <div class="drag" @mousedown="(e) => onMousedown(e, item, i)" @touchstart="(e) => onTouchstart(e, item, i)"></div>
        </div>
        <Iconfont v-if="(item as any).icon" :type="(item as any).icon" />
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.drag_bar {
  width: 40px;
  text-align: center;

  .bar_group {
    width: auto;
    padding: 6px;
    box-sizing: border-box;
    position: absolute;
    left: 50%;
    bottom: 100%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.54);
    color: hsla(0, 0%, 100%, 0.8);
    max-width: 200px;
    display: none;
  }

  &:hover .bar_group {
    display: flex;
  }

  .drag_container {
    height: 100%;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    .iconfont {
      font-size: 14px;
      color: #fff;
      margin-top: 4px;
    }
  }

  .outer {
    width: 4px;
    height: 88px;
    border-radius: 99px;
    background-color: hsla(0, 0%, 100%, 0.3);
    margin: 0 6px;
    position: relative;
  }

  .inner,
  .drag {
    width: 100%;
    height: 100%;
    max-height: 100%;
    border-radius: 99px;
    background: #fa1f41;
    position: absolute;
    left: 0;
    bottom: 0;
  }

  .drag {
    background: unset;
  }

  .inner:after {
    content: "";
    display: block;
    position: absolute;
    width: 10px;
    height: 10px;
    border-radius: 99px;
    background-color: #fff;
    left: 50%;
    top: 0;
    transform: translate(-50%, -50%);
  }
}
</style>
