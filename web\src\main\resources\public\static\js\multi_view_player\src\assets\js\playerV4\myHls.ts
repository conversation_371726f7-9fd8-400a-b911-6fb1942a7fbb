import Hls from 'hls.js';
import { TSDemux, FLVDemux, MP4Demux, Events } from "demuxer";
import { formatSeconds } from "@/utils";

class MyHls {
  hls: any;
  on: any;
  currentTime: number;
  play: () => void;
  pause: () => void;
  constructor(option: any) {
    this.currentTime = 0
    this.play = () => { }
    this.pause = () => { }
    this.init(option)
  }
  init(option: any) {
    this.hls = new Hls();
    // console.log(option)
    let video = document.createElement('video');
    //useHls 强制使用解码器解析
    if (Hls.isSupported() || option.useHls) {
      option.el.appendChild(video)
      var hls: any = new Hls();
      this.on = hls.on.bind(hls)
      this.hls = hls
      hls.loadSource(option.url);
      hls.attachMedia(video);
      // console.log(hls)
      video.addEventListener('pause', function () {
        hls.emit('pause')
      })
      video.addEventListener('play', function () {
        hls.emit('play')
      })
      hls.on(Hls.Events.MANIFEST_PARSED, function () {
        if (option.autoplay) {
          video.play();
        }
      });
      this.getCurrentTime(hls)
    } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
      // 如果浏览器原生支持HLS
      video.src = option.url;
      video.addEventListener('loadedmetadata', function () {
        if (option.autoplay) {
          video.play();
        }
      });
    } else {
      console.error('浏览器不支持HLS播放')
    }
    this.play = () => {
      video.play()
    }
    this.pause = () => {
      video.pause()
    }
  }
  getCurrentTime(hls: any) {
    const demux = new TSDemux({
      // debug: true
    });
    demux.on(Events.DEMUX_DATA, (e: any) => {
      const time = e.pes.PTS / 90000;
      let ss = formatSeconds(time);
      // console.log(ss);
    });
    hls.on(Hls.Events.FRAG_LOADED, function (event: any, data: any) {
      demux.push(data.payload, {
        // 本解码器支持推送部分数据
        // 当done设置为true后，如果数据解码完毕没有剩余数据，则认为数据已经推送完毕，Events.DONE才会发出。
        // 当done设置为false后，Events.DONE不会发出，等待后续数据推送
        done: true
      });
    });
  }

}

export default MyHls;