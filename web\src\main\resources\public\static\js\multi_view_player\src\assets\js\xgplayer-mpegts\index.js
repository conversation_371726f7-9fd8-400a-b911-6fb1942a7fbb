import { inherits as _inherits, createSuper as _createSuper, createClass as _createClass, classCallCheck as _classCallCheck } from "./_virtual/_rollupPluginBabelHelpers.js";
import { Events, BasePlugin, Errors } from "xgplayer";
import mpegts from "mpegts.js";
export { default as Flv } from "flv.js";
try {
  Flv.LoggingControl.enableAll = false;
} catch (e) {
}
var MpegtsJsPlugin = /* @__PURE__ */ function(_BasePlugin) {
  _inherits(MpegtsJsPlugin2, _BasePlugin);
  var _super = _createSuper(MpegtsJsPlugin2);
  function MpegtsJsPlugin2() {
    _classCallCheck(this, MpegtsJsPlugin2);
    return _super.apply(this, arguments);
  }
  _createClass(MpegtsJsPlugin2, [{
    key: "beforePlayerInit",
    value: function beforePlayerInit() {
      if (this.playerConfig.url) {
        this.flvLoad(this.playerConfig.url);
      }
    }
  }, {
    key: "afterCreate",
    value: function afterCreate() {
      var _this = this;
      var player = this.player;
      this.flv = null;
      player.video.addEventListener("contextmenu", function(e) {
        e.preventDefault();
      });
      this.on(Events.URL_CHANGE, function(url) {
        if (/^blob/.test(url)) {
          return;
        }
        player.once(Events.LOADED_DATA, function() {
          player.play();
        });
        _this.playerConfig.url = url;
        _this.flvLoad(url);
      });
      try {
        BasePlugin.defineGetterOrSetter(player, {
          url: {
            get: function get() {
              try {
                return _this.player.video.src;
              } catch (error) {
                return null;
              }
            },
            configurable: true
          }
        });
      } catch (e) {
      }
    }
  }, {
    key: "destroy",
    value: function destroy() {
      var player = this.player;
      this.destroyInstance();
      BasePlugin.defineGetterOrSetter(player, {
        url: {
          get: function get() {
            try {
              return player.__url;
            } catch (error) {
              return null;
            }
          },
          configurable: true
        }
      });
    }
  }, {
    key: "destroyInstance",
    value: function destroyInstance() {
      if (!this.flv) {
        return;
      }
      var player = this.player;
      this.flv.unload();
      this.flv.detachMediaElement(player.video);
      this.flv.destroy();
      player.__flv__ = null;
      this.flv = null;
    }
  }, {
    key: "createInstance",
    value: function createInstance(flv) {
      var player = this.player;
      if (!flv) {
        return;
      }
      flv.attachMediaElement(player.video);
      flv.load();
      flv.play();
      flv.on(Flv.Events.ERROR, function(e) {
        player.emit("error", new Errors("other", player.config.url));
      });
      flv.on(Flv.Events.LOADED_SEI, function(timestamp, data) {
        player.emit("loaded_sei", timestamp, data);
      });
      flv.on(Flv.Events.STATISTICS_INFO, function(data) {
        player.emit("statistics_info", data);
      });
      flv.on(Flv.Events.MEDIA_INFO, function(data) {
        player.mediainfo = data;
        player.emit("MEDIA_INFO", data);
      });
    }
  }, {
    key: "flvLoad",
    value: function flvLoad(newUrl) {
      var mediaDataSource = this.config.mediaDataSource;
      mediaDataSource.segments = [{
        cors: true,
        duration: void 0,
        filesize: void 0,
        timestampBase: 0,
        url: newUrl,
        withCredentials: false
      }];
      mediaDataSource.url = newUrl;
      mediaDataSource.isLive = this.playerConfig.isLive;
      this.flvLoadMds(mediaDataSource);
    }
  }, {
    key: "flvLoadMds",
    value: function flvLoadMds(mediaDataSource) {
      var player = this.player;
      if (typeof this.flv !== "undefined") {
        this.destroyInstance();
      }
      this.flv = player.__flv__ = Flv.createPlayer(mediaDataSource, this.flvConfig);
      this.createInstance(this.flv);
      this.flv.attachMediaElement(player.video);
      this.flv.load();
    }
  }, {
    key: "switchURL",
    value: function switchURL(url) {
      var player = this.player, playerConfig = this.playerConfig;
      var curTime = 0;
      if (!playerConfig.isLive) {
        curTime = player.currentTime;
      }
      player.flvLoad(url);
      player.video.muted = true;
      this.once("playing", function() {
        player.video.muted = false;
      });
      this.once("canplay", function() {
        if (!playerConfig.isLive) {
          player.currentTime = curTime;
        }
        player.play();
      });
    }
  }], [{
    key: "isSupported",
    get: function get() {
      return Flv.isSupported;
    }
  }, {
    key: "pluginName",
    get: function get() {
      return "MpegtsJsPlugin";
    }
  }, {
    key: "defaultConfig",
    get: function get() {
      return {
        mediaDataSource: {
          type: "flv"
        },
        flvConfig: {}
      };
    }
  }]);
  return MpegtsJsPlugin2;
}(BasePlugin);
export { MpegtsJsPlugin as default };
