import PlayerV3 from "xgplayer";
import FlvPlugin from "xgplayer-flv";
import MyHls from './myHls'

class Player {
  options: any;
  player: any;
  on: any;
  liveType: string;
  currentTime: number;
  play: any;
  pause: any;
  emit: any;
  constructor(option: {
    name: string,
    url: string,
    el: any,
    id: string,
  }) {
    this.init(option)
    this.liveType = ''
    this.currentTime = 0
  }
  init(option: any) {
    this.options = option
    const { url } = option
    let playerOption: any = {
      ...option
    };
    if (url.split("?")[0].includes(".flv") || !url.split("?")[0].includes(".m3u8")) {
      if (url.split("?")[0].includes(".flv")) {
        this.liveType = "flv"
      }
      // flv使用xgplayer v3
      playerOption.plugins = [...playerOption.plugins, FlvPlugin];
      playerOption.flv = {
        retryCount: 10, // 重试 3 次，默认值
        retryDelay: 3000, // 每次重试间隔 1 秒，默认值
        loadTimeout: 60000, // 请求超时时间为 10 秒，默认值
        fetchOptions: {
          // 该参数会透传给 fetch，默认值为 undefined
          mode: "cors",
        },
        maxReaderInterval: 10000, // 默认值 5000 毫秒
        targetLatency: 5, // 直播目标延迟，默认 5 秒
        maxLatency: 20, // 直播允许的最大延迟，默认 10 秒
        disconnectTime: 10, // 直播断流时间，默认 0 秒，（独立使用时等于 maxLatency）
      };
      this.player = new (PlayerV3 as any)(playerOption);
    } else if (url.split("?")[0].includes(".m3u8")) {
      //hls使用原生
      this.liveType = "hls"
      this.player = new MyHls(playerOption);
      console.log('this.player', this.player)
      this.on = this.player.on
      this.emit = this.player.emit
      this.play = this.player.play
      this.pause = this.player.pause;
    }
  }
}

export default Player;