:root {
  --mvp-active-color: #549CFF
}
.multi_view_player_container {
  overflow: hidden;
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  background-color: #000;
  font-size: 14px;
  --primary-color: #549CFF;


  .video_container{
    width: 100%;
    height: 0px;
    max-width:100%;
    padding-top: 56.25%;
  }

  .tool_bar {
    position: absolute;
    width: 100%;
    height: 40px;
    bottom: 0;
    z-index: 999;
    display: flex;
    align-items: center;
    background-image: linear-gradient(180deg, transparent, rgba(0, 0, 0, .37), rgba(0, 0, 0, .75), rgba(0, 0, 0, .75));
    color: #fff;
    padding: 0 10px;
    box-sizing: border-box;
    justify-content: flex-end;
    transition: .5s;

    input[type="checkbox"]:checked {
      position: relative;
      box-sizing: border-box;

      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: var(--primary-color);
      }

      &::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 60%;
        height: 20%;
        border-left: 2px solid #fff;
        border-bottom: 2px solid #fff;
        transform: translate(-50%, -70%) rotate(-45deg);
      }
    }

    .multi_view_player_time {
      color: #fff;
      margin-left: 10px;
      margin-right: auto;
      user-select: none;
    }

    .iconfont {
      font-size: 20px;
    }

    .sound_source,
    .frame,
    .speed,
    .volume,
    .drag_bar,
    .full_screen,
    .text_track {
      position: relative;
      margin-right: 20px;
      cursor: pointer;
      line-height: 1;
    }

    .volume.muted:before {
      content: '';
      position: absolute;
      width: 60%;
      height: 2px;
      background-color: #fff;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%) rotate(45deg);
      user-select: none;
      z-index: -1;
    }

    .text_track svg {
      width: 20px;
    }

    .full_screen {
      width: 30px;
      margin-right: 0;

      svg {
        width: 100%;
      }
    }

    .speed span {
      display: inline-block;
      width: 60px;
      line-height: 24px;
      text-align: center;
      background: rgba(0, 0, 0, .38);
      border-radius: 99px;
    }

    .progress {
      position: absolute;
      top: -10px;
      left: 15px;
      width: calc(100% - 30px);
      height: 4px;
      background-color: hsla(0, 0%, 100%, .3);
      border-radius: 99px;

      &:hover {
        height: 6px;
        top: -11px;

        .drag.fragmenter_list:not(.fragmenter) .fragment_bar {
          height: 6px;
        }
      }

      .outer {
        height: 100%;
        width: 100%;
        border-radius: 99px;
        background-color: hsla(0, 0%, 100%, .3);

        &.outer_fragment_list:not(.outer_fragmenter) {
          .inner {
            background-color: transparent;
          }

          .fragment_item_inner {
            background-color: #fa1f41;
          }
        }
      }

      .inner,
      .drag {
        width: 100%;
        height: 100%;
        max-width: 100%;
        border-radius: 99px;
        background: #fa1f41;
        position: absolute;
        left: 0;
        bottom: 0;
        cursor: pointer;
      }

      .drag {
        background: unset;

        &.fragmenter_list {
          background-color: transparent;

          &:not(.fragmenter) {
            .fragment_bar {
              top: unset;
              bottom: 0;
              background-color: transparent;
            }
          }

          .fragment_bar {
            display: block;

            .fragment_item {
              background-color: #fff;
            }
          }
        }

        &.fragmenter {
          .fragment_bar {
            display: block;

            .fragment_item {
              background-color: var(--primary-color);
            }

          }
        }

        .fragment_bar {
          width: 100%;
          height: 4px;
          position: absolute;
          left: 0;
          top: -10px;
          border-radius: 99px;
          background-color: hsla(0, 0%, 100%, .3);
          display: none;
          pointer-events: none;

          .fragment_item {
            height: 100%;
            background-color: var(--primary-color);
            border-radius: 99px;
            position: absolute;
            top: 0;

            .fragment_item_inner {
              height: 100%;
              border-radius: 99px;
            }
          }
        }
      }

      &:hover .drag {
        height: 200%;
      }

      .inner:after {
        content: '';
        display: block;
        position: absolute;
        width: 10px;
        height: 10px;
        border-radius: 99px;
        background-color: #fff;
        top: 50%;
        right: 0;
        transform: translate(50%, -50%);
        draggable: false;
        z-index: 100;
        pointer-events: none;
      }
    }


    .hover_list {
      position: relative;
      margin-right: 20px;
      display: flex;
      align-items: center;

      .modal {
        position: absolute;
        left: 50%;
        bottom: 100%;
        transform: translateX(-50%);
        background-color: rgba(0, 0, 0, .54);
        color: hsla(0, 0%, 100%, .8);
        max-width: 200px;
        display: none;
      }

      &:hover .modal {
        display: block;
      }

      &>span {
        cursor: pointer;
      }

      &>.iconfont {
        cursor: pointer;
      }

      p {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 100%;
        padding: 4px;
        margin: 0;
        cursor: pointer;

        &.active {
          color: var(--mvp-active-color);
          background-color: #ffffffc0;
        }

        label {
          margin-left: 4px;
          vertical-align: middle;
          cursor: pointer;
        }

        input {
          vertical-align: middle;
          cursor: pointer;
        }
      }
    }

    .danmu_btn {
      width: 30px;
      height: 16px;
      border-radius: 20px;
      cursor: pointer;
      font-size: 12px;
      margin-right: 20px;
      background-color: #333;
      padding: 1px;
      position: relative;
      display: flex;
      align-items: center;

      &.active {
        background-color: var(--primary-color);
      }

      &::before {
        content: '弹幕';
        position: absolute;
        top: 50%;
        right: 36px;
        transform: translateY(-50%);
        white-space: nowrap;
      }

      &::after {
        content: '';
        display: inline-block;
        width: 14px;
        height: 14px;
        border-radius: 50%;
        text-align: center;
        background-color: #fff;
        transition: .3s;
        margin-left: 0;
      }

      &.active::after {
        margin-left: auto;
      }
    }
  }

  &:hover .tool_bar {
    bottom: 0;
  }

  .xgplayer-error {
    display: none;
  }

  .xgplayer-start {
    display: none !important;
  }

  .center_play_btn {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, .38);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    cursor: pointer;

    .iconfont {
      color: #fff;
      font-size: 20px;
    }
  }

  .player_group {
    overflow: hidden;
    position: absolute;
    width: 100%;
    height: calc(100% - 40px);
    left: 0;
    top: 0;

    .player_item {
      position: relative;
      max-height: 100%;
      overflow: hidden;
      display: flex;
      align-items: center;

      &[data-sort="0"],
      &[data-sort="1"],
      &[data-sort="2"] {
        position: absolute;
      }

      .item_tool_bar {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        color: #fff;
        z-index: 126;
        background-color: rgba(0, 0, 0, .3);
        padding-right: 5px;
        box-sizing: border-box;
        display: none;
        height: 20px;
        line-height: 20px;

        .screen_select {
          position: relative;
          height: 100%;
          padding-left: 5px;

          .selected {
            color: #fff;

            &::after {
              content: '';
              display: inline-block;
              width: 8px;
              height: 8px;
              border-bottom: 2px solid #fff;
              border-right: 2px solid #fff;
              transform: rotate(-135deg);
              margin-bottom: 3px;
              margin-left: 8px;
              transition: .3s;
              scale: -1;
            }
          }

          .select_list {
            position: absolute;
            display: none;
            background-color: rgba(0, 0, 0, .3);
            padding: 5px 10px;
            left: 0;
            top: 20px;
            overflow-y: auto;

            &::-webkit-scrollbar {
              width: 0;
            }

            .select_item {
              cursor: pointer;
              white-space: nowrap;

              &+.select_item {
                margin-top: 6px;
              }

              &.disabled {
                color: #bbb;
                pointer-events: none;
                cursor: not-allowed;
              }
            }
          }

          &:hover {
            .selected {
              &::after {
                transform: rotate(-135deg);
                scale: 1;
                margin-bottom: -2px;
              }
            }

            .select_list {
              display: block;
            }
          }
        }

        .item_tool_bar_right {
          margin-left: auto;

        }

        .iconfont {
          font-size: 18px;

          &+.iconfont {
            margin-left: 6px;
          }
        }
      }

      &:hover .item_tool_bar {
        display: flex;
      }

      .video_name {
        padding: 0 4px;
      }
    }
  }

  // &:hover .text_track_content {
  //   transform: translate(-50%, 0);
  // }

  .danmu_container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: calc(100% - 40px);
    z-index: 9;
    pointer-events: none;
  }

  .video_container {

    video {
      width: 100%;
      left: 0;
      top: 0;
      height: 100%;
      position: absolute;
      background-color: #000;
    }

    .error_tips {
      width: 100%;
      left: 0;
      top: 0;
      height: 100%;
      position: absolute;
      background-color: #000;

      .no_address {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #fff;

        span {
          margin-top: 20px;
        }

        img {
          width: 50%;
        }
      }
    }

    &.fit_fill {
      video {
        object-fit: fill;
      }
    }
  }
}

.multi_view_player_container {

  &[num="3"] [data-sort="0"] {
    width: 66.66% !important;
    padding-top: 0 !important;
    left: 0%;
    top: 50%;
    transform: translateY(-50%);
  }

  &[num="3"] [data-sort="1"] {
    width: 33.33% !important;
    padding-top: 0 !important;
    left: 66.66%;
    bottom: 50%;
    max-height: 50%;
  }

  &[num="3"] [data-sort="2"] {
    width: 33.33% !important;
    padding-top: 0 !important;
    left: 66.66%;
    top: 50%;
    max-height: 50%;
  }

  &[num="1"] [data-sort="0"] {
    width: 100% !important;
    height: 100%;
    left: 0;
    top: 50%;
    transform: translateY(-50%);

    .video_container {
      padding-top: 0 !important;
      height: 100% !important;
    }
  }

  &.height_fix[num="1"] [data-sort="0"] {
    height: 100%;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
  }

  &.height_fix[num="1"] [data-sort="0"] .video_container {
    padding-top: unset !important;
    height: 100% !important;
  }

  [data-sort="-1"] {
    display: none !important;
  }

  &.display_style_1 {

    &[num="2"] [data-sort="0"] {
      top: 50%;
      transform: translateY(-50%);
      width: 66.66% !important;
      left: 0%;
      max-height: 100%;
    }

    &[num="2"] [data-sort="1"] {
      width: 33.33% !important;
      left: 66.66%;
      bottom: 50%;
      max-height: 50%;
    }
  }

  &.display_style_2 {

    &[num="2"] [data-sort="0"],
    &[num="2"] [data-sort="1"] {
      width: 50% !important;
      top: 50%;
      transform: translateY(-50%);
      overflow: hidden;
    }

    &[num="2"] [data-sort="0"] {
      left: 0%;
    }

    &[num="2"] [data-sort="1"] {
      left: 50%;
    }
  }
}