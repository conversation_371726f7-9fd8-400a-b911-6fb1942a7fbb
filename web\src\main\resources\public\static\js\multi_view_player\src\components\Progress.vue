<script lang="ts" setup>
import { reactive, ref, watch } from "vue";
import { debounce, throttle } from "@/utils/index";

const props = defineProps({
  currentTime: { type: Number, default: 0 },
  duration: { type: Number, default: 0 },
  fragmentList: {
    type: Array,
    default: () => {
      return [];
    },
  },
});

const emit = defineEmits(["seek", "emit"]);

const ifDraging = ref(false);
const dragWidth = ref(0);
const outerRef = ref(null);
const fragmentStatus = ref(false);
const fragmentList = reactive<any[]>([]);
const currentFragment = reactive<any[]>([]);
const currentFragmentType = ref(0); // 0-开始，1-结束
//进度条
const onProgressDragMouseDown = (e: any) => {
  if (e.stopPropagation) e.stopPropagation();
  if (e.preventDefault) e.preventDefault();
  let zoom = parseFloat((document as any).body.style.zoom || 1);
  ifDraging.value = true;
  let width = (outerRef as any).value?.clientWidth;
  dragWidth.value = ((e.offsetX / width) * 100) / zoom;
  document.onmousemove = function (e) {
    dragWidth.value = ((e.offsetX / width) * 100) / zoom;
    debounce(emit("seek", ((e.offsetX / width) * props.duration) / zoom), 50);
  };
  document.onmouseup = function (e) {
    emit("seek", ((e.offsetX / width) * props.duration) / zoom);
    ifDraging.value = false;
    document.onmousemove = null;
    document.onmouseup = null;
    if (fragmentStatus.value) {
      //标记判断点
      handleFragment(((e.offsetX / width) * props.duration) / zoom);
    }
  };
};

const getFragmentWidth = (item: any) => {
  if (!item || item.length != 2) {
    return 0;
  }
  let space = item[1] - item[0];
  if (!space || space <= 0 || isNaN(space)) {
    return 0;
  }
  return (space / props.duration) * 100 + "%";
};

const getFragmentInnerWidth = (item: any) => {
  if (item && item.length == 2) {
    if (item[1] < props.currentTime) {
      return "100%";
    } else if (item[0] > props.currentTime) {
      return 0;
    } else {
      return ((props.currentTime - item[0]) / (item[1] - item[0])) * 100 + "%";
    }
  } else {
    return 0;
  }
};

const getFragmentLeft = (item: any) => {
  if (!item || item.length != 2) {
    return 0;
  }
  if (!item[0] || item[0] <= 0 || isNaN(item[0])) {
    return 0;
  }
  return (item[0] / props.duration) * 100 + "%";
};

//获取标记点
const handleFragment = (time: number) => {
  const t = parseInt(time as any);
  if (currentFragmentType.value == 1 && t <= currentFragment[0]) {
    return;
  }
  currentFragment[currentFragmentType.value] = t;
  if (currentFragment[0] && !currentFragment[1]) {
    currentFragment[1] = props.duration;
  }
  if (!currentFragment[0] && currentFragment[1]) {
    currentFragment[0] = 0;
  }
  emit("emit", "fragment", t);
};

const sortFragmentList = (list: any[][]) => {
  //按起始点排序
  return [...list].sort((a, b) => {
    return a[0] - b[0];
  });
};

//开始标记片段 0-开始，1-结束
const startFragment = (type: 0 | 1) => {
  currentFragmentType.value = type || 0;
  fragmentStatus.value = true;
};

//结束并保存标记片段
const stopFragment = (item: any) => {
  return new Promise((res) => {
    let fragment = [...(item ?? currentFragment)];
    currentFragment.length = 0;
    fragmentStatus.value = false;
    //判断正在播放进度超出了当前标记片段区域，跳至标记区域
    if (props.currentTime < fragment[0] || props.currentTime > fragment[1]) {
      emit("seek", fragment[0]);
    }
    let oldList = [...fragmentList];
    fragmentList.length = 0;
    sortFragmentList([...oldList, fragment]).map((el) => fragmentList.push(el));
    res(fragment);
  });
};

const setFragmentList = (list: any[]) => {
  fragmentList.length = 0;
  if (currentFragment[0] || currentFragment[1]) {
    currentFragment.length = 0;
  }
  sortFragmentList([...list]).map((el) => {
    fragmentList.push(el);
  });
};

watch(
  () => props.fragmentList,
  (newList: any[], oldList) => {
    if (newList && JSON.stringify(newList) != JSON.stringify(oldList)) {
      setFragmentList(newList);
    }
  },
  {
    immediate: true,
  }
);

//超出了播放范围则跳转到下一个标记区域
const onTimeUpdate = throttle(function (time: any) {
  if (!fragmentList.length || fragmentStatus.value) return;
  let flag = !!fragmentList.find((item: any) => time > item[0] && time < item[1]);
  if (flag) return;
  //超出了标记区域
  let index = 0;
  for (let i = 0; i < fragmentList.length; i++) {
    let el = fragmentList[i];
    if (el[0] > time) {
      index = i;
      break;
    }
  }
  emit("seek", fragmentList[index][0] ?? 0);
}, 1000);

watch(
  () => props.currentTime,
  (newTime) => {
    if (!newTime) return;
    onTimeUpdate(newTime);
  },
  {
    immediate: true,
  }
);

defineExpose({
  startFragment,
  stopFragment,
  setFragmentList,
});
</script>

<template>
  <div class="progress">
    <div class="outer" :class="{ outer_fragment_list: fragmentList.length, outer_fragmenter: fragmentStatus }" ref="outerRef">
      <div class="inner" :style="{ width: (ifDraging ? dragWidth : (props.currentTime / props.duration) * 100) + '%' }"></div>
      <div class="drag" :class="{ fragmenter_list: fragmentList.length, fragmenter: fragmentStatus }" @mousedown="onProgressDragMouseDown">
        <div class="fragment_bar">
          <div class="fragment_item" v-for="item in fragmentList" :style="{ width: getFragmentWidth(item), left: getFragmentLeft(item) }">
            <div class="fragment_item_inner" :style="{ width: getFragmentInnerWidth(item) }"></div>
          </div>
          <div
            class="fragment_item current_fragment"
            v-if="currentFragment[0] || currentFragment[1]"
            :style="{ width: getFragmentWidth(currentFragment), left: getFragmentLeft(currentFragment) }"
          >
            <div class="fragment_item_inner" :style="{ width: getFragmentInnerWidth(currentFragment) }"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
