import vttToJson from "@/utils/vtt-to-json";

/**
 * 获取静态资源请求的通用请求头
 * @returns 包含sobey-resources标识的请求头对象
 */
export const getResourceHeaders = () => {
  return {
    'sobey-resources': Date.now().toString()
  }
}

/**
 * 获取静态资源请求的fetch配置选项
 * @param additionalOptions 额外的fetch选项
 * @returns 包含请求头的fetch配置
 */
export const getResourceFetchOptions = (additionalOptions: RequestInit = {}) => {
  return {
    ...additionalOptions,
    headers: {
      ...getResourceHeaders(),
      ...(additionalOptions.headers || {})
    }
  }
}

export const formatSeconds = (value: any) => {
  if (value == 'Infinity') value = 0
  let result = ''
  let hour = Math.floor(value / 3600);
  let minute = Math.floor(value % 3600 / 60);
  let second = Math.floor(value % 60);
  if (hour > 0) result += hour + ':';
  if (minute < 10) result += '0';
  result += minute + ':';
  if (second < 10) result += '0';
  result += second;
  return result;
}


export const reqVttText = (url: string) => {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();
    xhr.open('GET', url, true);
    xhr.responseType = 'text'

    // 添加sobey-resources请求头
    const headers = getResourceHeaders();
    Object.keys(headers).forEach(key => {
      xhr.setRequestHeader(key, headers[key as keyof typeof headers]);
    });

    xhr.onreadystatechange = async function () {
      if (xhr.readyState === 4 && xhr.status === 200) {
        let data = [];
        try {
          data = await vttToJson(xhr.response)
        } catch { }
        data.map((el: any) => {
          el.part = el.part.replaceAll('\r', '')
        })
        resolve(data);
      } else if (xhr.readyState === 4) {
        resolve([]);
      }
    };
    xhr.send();
  })
}

export const debounce = (func: any, wait: number) => {
  let timeout: ReturnType<typeof setTimeout> | null = null;
  return function (this: any, ...args: any) {
    if (timeout !== null) {
      clearTimeout(timeout);
    }
    timeout = setTimeout(() => func.apply(this, args), wait);
  };
};

export const throttle = (func: Function, limit: number) => {
  let inThrottle: boolean = false;
  return function (this: any, ...args: any) {
    if (inThrottle) return;
    inThrottle = true;
    func.apply(this, args);
    setTimeout(() => (inThrottle = false), limit);
  };
};

export const EventEmitter = class {
  events: any;
  constructor() {
    this.events = {};
  }
  on(eventName: string, listener: any) {
    if (!this.events[eventName]) {
      this.events[eventName] = [];
    }
    this.events[eventName].push(listener);
  }
  emit(eventName: any, ...args: any) {
    const listeners = this.events[eventName];
    if (listeners) {
      listeners.forEach((listener: any) => listener(...args));
    }
  }
  off(eventName: string, listener: any) {
    const listeners = this.events[eventName];
    if (listeners) {
      this.events[eventName] = listeners.filter((l: any) => l !== listener);
    }
  }
}

export const canAutoPlay = () => {
  const ctx = new AudioContext()
  let ifCanAutoPlay = ctx.state === 'running'
  ctx.close()
  return ifCanAutoPlay
}

/**
 * 初始化全局网络请求拦截器
 * 为所有的XMLHttpRequest和fetch请求添加sobey-resources请求头
 */
export const initGlobalRequestInterceptor = () => {
  // 拦截XMLHttpRequest
  const originalXHROpen = XMLHttpRequest.prototype.open;
  const originalXHRSend = XMLHttpRequest.prototype.send;

  XMLHttpRequest.prototype.open = function(method: string, url: string | URL, async: boolean = true, user?: string | null, password?: string | null) {
    (this as any)._url = url.toString();
    return originalXHROpen.call(this, method, url, async, user, password);
  };

  XMLHttpRequest.prototype.send = function(body?: Document | XMLHttpRequestBodyInit | null) {
    // 只为静态资源请求添加请求头
    if ((this as any)._url && isStaticResource((this as any)._url)) {
      const headers = getResourceHeaders();
      Object.keys(headers).forEach(key => {
        this.setRequestHeader(key, headers[key as keyof typeof headers]);
      });
    }
    return originalXHRSend.call(this, body);
  };

  // 拦截fetch请求
  const originalFetch = window.fetch;
  window.fetch = function(input: RequestInfo | URL, init?: RequestInit): Promise<Response> {
    const url = typeof input === 'string' ? input : input instanceof URL ? input.toString() : (input as Request).url;

    // 只为静态资源请求添加请求头
    if (isStaticResource(url)) {
      const headers = getResourceHeaders();
      init = init || {};
      init.headers = {
        ...headers,
        ...(init.headers || {})
      };
    }

    return originalFetch.call(this, input, init);
  };
}

/**
 * 判断是否为静态资源请求
 * @param url 请求URL
 * @returns 是否为静态资源
 */
const isStaticResource = (url: string): boolean => {
  // 静态资源文件扩展名
  const staticExtensions = [
    '.mp4', '.flv', '.m3u8', '.ts', '.webm', '.avi', '.mov', '.wmv', // 视频文件
    '.mp3', '.wav', '.aac', '.ogg', '.flac', // 音频文件
    '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg', // 图片文件
    '.css', '.js', '.woff', '.woff2', '.ttf', '.eot', // 样式和字体文件
    '.vtt', '.srt', '.ass', // 字幕文件
    '.json', '.xml' // 数据文件
  ];

  // 检查URL是否包含静态资源扩展名
  const lowerUrl = url.toLowerCase();
  return staticExtensions.some(ext => lowerUrl.includes(ext)) ||
         lowerUrl.includes('/bucket') || // 存储桶资源
         lowerUrl.includes('/static') || // 静态资源目录
         lowerUrl.includes('/assets'); // 资源目录
}

// 获取是否是pc端页面
export const getIfPc = () => {
  const width = window.innerWidth;
  if (width > 768) {
    return true;
  } else {
    return false;
  }
};