import vttToJson from "@/utils/vtt-to-json";
export const formatSeconds = (value: any) => {
  if (value == 'Infinity') value = 0
  let result = ''
  let hour = Math.floor(value / 3600);
  let minute = Math.floor(value % 3600 / 60);
  let second = Math.floor(value % 60);
  if (hour > 0) result += hour + ':';
  if (minute < 10) result += '0';
  result += minute + ':';
  if (second < 10) result += '0';
  result += second;
  return result;
}


export const reqVttText = (url: string) => {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();
    xhr.open('GET', url, true);
    xhr.responseType = 'text'
    xhr.onreadystatechange = async function () {
      if (xhr.readyState === 4 && xhr.status === 200) {
        let data = [];
        try {
          data = await vttToJson(xhr.response)
        } catch { }
        data.map((el: any) => {
          el.part = el.part.replaceAll('\r', '')
        })
        resolve(data);
      } else if (xhr.readyState === 4) {
        resolve([]);
      }
    };
    xhr.send();
  })
}

export const debounce = (func: any, wait: number) => {
  let timeout: ReturnType<typeof setTimeout> | null = null;
  return function (this: any, ...args: any) {
    if (timeout !== null) {
      clearTimeout(timeout);
    }
    timeout = setTimeout(() => func.apply(this, args), wait);
  };
};

export const throttle = (func: Function, limit: number) => {
  let inThrottle: boolean = false;
  return function (this: any, ...args: any) {
    if (inThrottle) return;
    inThrottle = true;
    func.apply(this, args);
    setTimeout(() => (inThrottle = false), limit);
  };
};

export const EventEmitter = class {
  events: any;
  constructor() {
    this.events = {};
  }
  on(eventName: string, listener: any) {
    if (!this.events[eventName]) {
      this.events[eventName] = [];
    }
    this.events[eventName].push(listener);
  }
  emit(eventName: any, ...args: any) {
    const listeners = this.events[eventName];
    if (listeners) {
      listeners.forEach((listener: any) => listener(...args));
    }
  }
  off(eventName: string, listener: any) {
    const listeners = this.events[eventName];
    if (listeners) {
      this.events[eventName] = listeners.filter((l: any) => l !== listener);
    }
  }
}

export const canAutoPlay = () => {
  const ctx = new AudioContext()
  let ifCanAutoPlay = ctx.state === 'running'
  ctx.close()
  return ifCanAutoPlay
}

// 获取是否是pc端页面
export const getIfPc = () => {
  const width = window.innerWidth;
  if (width > 768) {
    return true;
  } else {
    return false;
  }
};