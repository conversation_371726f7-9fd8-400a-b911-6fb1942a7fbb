<script lang="ts" setup>
import { onMounted, ref, watch } from "vue";
import type { IAdvertisementItem } from "@/main";

const props = defineProps<{
  currentTime: number;
  player: any;
  list: IAdvertisementItem[];
}>();
const currentAds = ref<IAdvertisementItem | null>(null);
const timer = ref(0);
const list = ref<IAdvertisementItem[]>([]);

onMounted(() => {
  list.value = [...(props.list ?? [])];
});

const getAds = (time: number) => {
  const ads = list.value.find((item: IAdvertisementItem) => item.start == parseInt(String(time)));
  if (ads && (!currentAds.value || currentAds.value.start != ads.start)) {
    currentAds.value = ads;
    showAds();
  }
};
const showAds = () => {
  if (!currentAds.value) return;
  try {
    props.player?.pause();
  } catch {}
  timer.value = currentAds.value?.duration ?? 0;
  let interval = setInterval(() => {
    timer.value -= 1;
    if (timer.value <= 0) {
      clearInterval(interval);
      list.value.splice(
        list.value.findIndex((el: IAdvertisementItem) => currentAds.value?.start === el.start),
        1
      );
      currentAds.value = null;
      props.player?.play();
    }
  }, 1000);
};

watch(
  () => props.currentTime,
  (newVal: number, oldVal: number) => {
    //监听oldVal防止seek播放无法播放广告
    if (oldVal || oldVal === 0) {
      getAds(oldVal);
    }
  }
);
</script>

<template>
  <div v-show="timer > 0" class="multi_advertisement">
    <div class="timer">
      <span>{{ timer }} 秒</span>
    </div>
    <img :src="currentAds?.cover" alt="" />
  </div>
</template>

<style lang="less" scoped>
.multi_advertisement {
  position: absolute;
  z-index: 99999999;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  background-color: #000;
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  .timer {
    position: absolute;
    width: 100%;
    top: 0;
    background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
    text-align: right;
    padding-right: 20px;
    line-height: 40px;
    color: #fff;
    box-sizing: border-box;
    font-size: 18px;
  }
}
</style>
