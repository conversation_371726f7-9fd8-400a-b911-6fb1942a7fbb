import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

// const baseUrl = 'https://course.ynu.edu.cn'
const baseUrl = 'http://172.16.151.202/'
// const baseUrl = 'https://study.hznu.edu.cn/'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
  ],
  build: {
    sourcemap: true,
    // lib: {
    //   entry: 'src/main.ts',
    //   name: 'multi_view_player',
    //   fileName: 'index',
    //   formats: ['es'],
    // },
    rollupOptions: {
      output: {
        entryFileNames: `index.js`, // 设置输出的入口文件名
        chunkFileNames: `index.js`, // 设置输出的代码分割块文件名
        assetFileNames: `index.[ext]`, // 设置输出的静态资源文件名
      },
    }
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  server: {
    port: 3000,
    proxy: {
      '/bucket': {
        target: baseUrl,
        changeOrigin: true,
      },
      '/rman': {
        target: baseUrl,
        changeOrigin: true,
      },
      '/learn': {
        target: baseUrl,
        changeOrigin: true,
      },
      '/bucket-z': {
        target: baseUrl,
        changeOrigin: true,
      },
    }
  }
})
