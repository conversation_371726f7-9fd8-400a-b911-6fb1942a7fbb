<script setup lang="ts">
import { computed, onMounted, reactive, ref, watch } from 'vue'
import <PERSON><PERSON><PERSON><PERSON> from 'danmu.js'
import PlayerV3 from 'xgplayer'
import FlvPlugin from 'xgplayer-flv'
import FlvJsPlugin from 'xgplayer-flv.js'
import HlsPlugin from 'xgplayer-hls.js'
import XgSubtitle from 'xgplayer-subtitles'
import Watermark from '@/assets/js/watermark.js'
import 'xgplayer/dist/index.min.css'
import Iconfont from '@/components/Iconfont.vue'
import { formatSeconds } from '@/utils'
import DragBar from './components/DragBar.vue'
import Progress from './components/Progress.vue'
import Advertisement from './components/Advertisement.vue'
import TextTrack from './components/TextTrack.vue'
import { TSDemux, FLVDemux, MP4Demux, Events } from 'demuxer'
import MpegtsPlugin, { type MpegtsPluginConfig } from 'xgplayer-mpegts.js'

//v2
import '@/assets/js/xgplayerV2/xgplayer'
import '@/assets/js/xgplayerV2/xgplayer-flv'
import '@/assets/js/xgplayerV2/xgplayer-hls'

import { getIfPc } from '@/utils'

const props = defineProps({
  option: {
    type: Object,
    default: {},
  },
  MultiViewPlayer: {
    type: Object,
    default: {},
  },
})

const option = reactive<any>({ ...props.option })
const currentUrls = ref<any[]>([])
const players = reactive<any[]>([])
const currentTime = ref(0)
const duration = ref(0)
const danmu = ref<any>(null)
const danmuRef = ref(null)
const playerDoms = reactive<any[]>([])
const status = ref(0) //0-未开始，1-播放中，2-暂停中
const playerGroupRef = ref<any>(null)
const volume = ref(0.6)
const oldVolume = ref(0.6)
const sortRule = ref<number[]>([])
const urls = ref<any[]>([]) //初始化urls
const activeVoice = ref(-2) //当前播放画面音源
const selectedVoice = ref(-1) //音源列表选中音源
const ifFullScreen = ref(false)
const containerRef = ref<any>(null)
const danmuOpen = ref(false)
const currentSpeed = ref(1)
const waterMark = ref<any>(null)
const muted = ref(false)
const progressRef = ref<any>(null)
//选中字幕语言
const textTrackLangs = ref<string[]>([])
const textTrackRef = ref<any>(null)
const textTrackSrcs = ref<any[]>([])

onMounted(() => {
  if (Object.keys(option).length) {
    urls.value = option.urls.map((el: any, index: number) => ({ ...el, index }))
    init(option)
  }
  initFullScreen()
  initDanmu()
  initWaterMark(playerGroupRef.value)
  props.MultiViewPlayer.screenshoot = screenshoot
  props.MultiViewPlayer.screenshootByName = screenshootByName
  props.MultiViewPlayer.destroy = destroy
  props.MultiViewPlayer.play = play
  props.MultiViewPlayer.pause = pause
  props.MultiViewPlayer.startFragment = progressRef.value.startFragment
  props.MultiViewPlayer.stopFragment = progressRef.value.stopFragment
  props.MultiViewPlayer.saveFragment = progressRef.value.saveFragment
  props.MultiViewPlayer.setConfig = setConfig
  props.MultiViewPlayer.seek = seek
  props.MultiViewPlayer.setMuted = setMuted
  props.MultiViewPlayer.pushTextTrack = pushTextTrack
  props.MultiViewPlayer.setOption = setOption
})

watch(
  () => sortRule.value,
  () => {
    if (sortRule.value.length !== players.length) {
      //初始化了的播放器数量不足，重新初始化播放器以供播放
      sortRule.value.map((urlIndex, i) => {
        if (!players[i]) {
          //不存在，重新初始化
          playerInit(urls.value[urlIndex], i)
          if (status.value == 1) {
            //播放中，直接播放
            players[i]?.once('canplay', function (p: any) {
              players[i].play()
            })
          }
        }
      })
    }
    players.map((p) => {
      if (sortRule.value.includes(p.index)) {
        p.ifShow = true
      } else {
        p.ifShow = false
      }
    })
    //重新布局
    props.MultiViewPlayer.emit(
      'change',
      sortRule.value.map((index: number) =>
        players.find((p) => p.index == index)
      )
    )
  }
)

/**
 * @param option 配置参数
 * @param option.fragmentList  片段列表
 */
const setConfig = (option: any) => {
  if (option.fragmentList) {
    progressRef.value.setFragmentList(option.fragmentList)
  }
}

const getPlayerRef: any = (i: number) => {
  return (el: HTMLElement | null) => {
    if (el) {
      playerDoms[i] = el
    }
  }
}

const initWaterMark = (dom:any) => {
  if (!option.watermark) return
  let options = {
    watermark_color: '#eee',
    // 传入的水印密度 计算水印行数和列数 默认值为5
    watermark_rows: ((option.watermark_density ?? 5) * (getIfPc() ? 4 : 2)) / 5,
    watermark_cols: ((option.watermark_density ?? 5) * (getIfPc() ? 3 : 2)) / 5,
    watermark_width: dom.clientWidth / 8, //水印宽度
    watermark_height: dom.clientHeight / 6, //水印长度
    watermark_parent_node: dom,
    watermark_txt: option.watermark,
    watermark_alpha: 0.35,
    watermark_fontsize: (dom.clientWidth / 1200) * 18 + 'px',
  }

  waterMark.value = new Watermark(options)
  let resizeObserver = new ResizeObserver((dom) => {
    waterMark.value?.load(options)
  })
  resizeObserver.observe(dom)
}

const init = (option: any) => {
  if (!urls.value.length) {
    console.error('缺少urls参数')
    return
  }
  //默认选前三路用于展示
  if (option.defaultFrame && option.defaultFrame.length) {
    let defaultFrame = option.defaultFrame.map((el: any) => {
      return el - 1
    })
    //配置了默认画面
    urls.value.map((el: any, i: number) => {
      if (currentUrls.value.length < 3 && defaultFrame.includes(i)) {
        currentUrls.value = [...currentUrls.value, el]
        sortRule.value = [...sortRule.value, i]
      }
    })
  } else {
    currentUrls.value = urls.value.slice(0, 3)
    sortRule.value = currentUrls.value.map((el: any, i: number) => i)
  }

  currentUrls.value.map((url: any, i: number) => {
    //初始化播放器
    playerInit(url, i)
  })

  setTimeout(() => {
    props.MultiViewPlayer.emit('ready', props.MultiViewPlayer)
  }, 100)
  // console.log("players", players);

  //默认音源
  if (option.defaultAudio && option.defaultAudio.length) {
    activeVoice.value = option.defaultAudio[0] - 1
    selectedVoice.value = activeVoice.value
  } else {
    //未配置默认音源则默认联动画面
    activeVoice.value = -1
    selectedVoice.value = -1
  }
}

const setOption = (opt: { textTrack?: any[] }) => {
  const { textTrack } = opt
  option.textTrack = textTrack
}

const playerInit = (url: any, i: number) => {
  if (!url || (players[i] && players[i]?.index == url.index)) return //已存在，直接使用
  if (players[i]) {
    try {
      players[i].destroy()
      players[i].flv?.destroy()
      players[i].hls?.destroy()
      players[i].errorDom?.remove()
      players[i] = null
    } catch (error) {
      console.error('播放器销毁失败', error)
    }
  }
  let playerOption: any = {
    ...option,
    name: url.name,
    el: playerDoms[i],
    url: url.error ? '' : url.path,
    autoplayMuted: false,
    // autoplay: false,
    controls: false,
    volume: option.volume || 0.6,
    closeVideoClick: true,
    plugins: [],
    playsinline: true,
    fluid: true,
    pip: true,
    muted: url.index === activeVoice.value,
    hasAudio: url.hasVoice ?? true,
  }
  delete playerOption.id
  delete playerOption.textTrack
  let player: any
  if (option.version == 2) {
    // xgplayer v2
    playerOption = {
      ...playerOption,
      autoplay: true,
      flvOptionalConfig: {
        isLive: option.isLive,
        enableWorker: true,
        enableStashBuffer: true, //启用缓存
        stashInitialSize: 4096, //缓存大小4m
        lazyLoad: false,
        lazyLoadMaxDuration: 40 * 60,
        autoCleanupSourceBuffer: true,
        autoCleanupMaxBackwardDuration: 35 * 60,
        autoCleanupMinBackwardDuration: 30 * 60,
      },
    }
    if (url.path.split('?')[0].includes('.flv')) {
      player = new (window as any).FlvJsPlayer(playerOption)
    } else if (url.path.split('?')[0].includes('.m3u8')) {
      playerOption.useHls = true
      player = new (window as any).HlsJsPlayer(playerOption)
    } else {
      player = new (window as any).Player(playerOption)
    }
  } else if (option.version == 3) {
    // xgplayer v3
    if (url.path.includes('ws://') || url.path.includes('wss://')) {
      playerOption.plugins = [...playerOption.plugins, FlvJsPlugin]
      //使用xplayer-flv.js插件
      playerOption.flvJsPlugin = {
        onlyVideo: true,
        enableAudio: true,
        hasVideo: true,
        hasAudio: url.hasVoice !== false,
      }
      playerOption.minWaitDelay = 10000
      playerOption.replay = false
    } else if (url.path.split('?')[0].includes('.flv')) {
      playerOption.plugins = [...playerOption.plugins, FlvPlugin]
      //使用xplayer-flv插件（）
      playerOption.flv = {
        retryCount: 10, // 重试 3 次，默认值
        retryDelay: 3000, // 每次重试间隔 1 秒，默认值
        loadTimeout: 60000, // 请求超时时间为 10 秒，默认值
        fetchOptions: {
          // 该参数会透传给 fetch，默认值为 undefined
          mode: 'cors',
        },
        maxReaderInterval: 10000, // 默认值 5000 毫秒
        targetLatency: 20, // 直播目标延迟，默认 5 秒
        maxLatency: 60, // 直播允许的最大延迟，默认 10 秒
        disconnectTime: 10, // 直播断流时间，默认 0 秒，（独立使用时等于 maxLatency）
        onlyVideo:
          url.hasVoice != null && url.hasVoice != undefined
            ? !url.hasVoice
            : false,
      }
      playerOption.replay = false
    } else if (url.path.split('?')[0].includes('.m3u8')) {
      playerOption.plugins = [...playerOption.plugins, HlsPlugin]
      //xgplayer-hls使用flv配置
      playerOption.hls = {
        retryCount: 10, // 重试 3 次，默认值
        retryDelay: 3000, // 每次重试间隔 1 秒，默认值
        loadTimeout: 60000, // 请求超时时间为 10 秒，默认值
        fetchOptions: {
          // 该参数会透传给 fetch，默认值为 undefined
          mode: 'cors',
        },
        targetLatency: 10, // 直播目标延迟，默认 10 秒
        maxLatency: 20, // 直播允许的最大延迟，默认 20 秒
        disconnectTime: 30, // 直播断流时间，默认 0 秒，（独立使用时等于 maxLatency）
        preloadTime: 30, // 默认值
        analyzeDuration: 30000,
        onlyVideo:
          url.hasVoice != null && url.hasVoice != undefined
            ? !url.hasVoice
            : false,
        useHls: true,
      }
      //xgplayer-hls.js使用hlsJsPlugin配置
      playerOption.hlsJsPlugin = {
        ...playerOption.hls,
        hlsOpts: {
          enableWorker: false,
        },
      }
    }
    player = new (PlayerV3 as any)(playerOption)
  } else if (option.version == 4) {
    // xgplayer v4
    if (
      url.path.split('?')[0].includes('.flv') ||
      url.path.includes('ws://') ||
      url.path.includes('wss://')
    ) {
      playerOption.plugins = [...playerOption.plugins, MpegtsPlugin]
      playerOption.MpegtsPlugin = {
        mediaDataSource: {
          type: 'flv',
          isLive: playerOption.isLive,
          hasVideo: true,
          hasAudio: url.hasVoice ?? true,
        },
        mpegtsConfig: {
          liveBufferLatencyChasing: true,
          enableWorker: false,
          liveSync: playerOption.isLive,
        },
      }
    } else if (url.path.split('?')[0].includes('.m3u8')) {
      playerOption.plugins = [...playerOption.plugins, HlsPlugin]
      //xgplayer-hls使用flv配置
      playerOption.hls = {
        retryCount: 10, // 重试 3 次，默认值
        retryDelay: 3000, // 每次重试间隔 1 秒，默认值
        loadTimeout: 60000, // 请求超时时间为 10 秒，默认值
        fetchOptions: {
          // 该参数会透传给 fetch，默认值为 undefined
          mode: 'cors',
        },
        targetLatency: 10, // 直播目标延迟，默认 10 秒
        maxLatency: 20, // 直播允许的最大延迟，默认 20 秒
        disconnectTime: 30, // 直播断流时间，默认 0 秒，（独立使用时等于 maxLatency）
        preloadTime: 30, // 默认值
        analyzeDuration: 30000,
        onlyVideo:
          url.hasVoice != null && url.hasVoice != undefined
            ? !url.hasVoice
            : false,
        useHls: true,
      }
      //xgplayer-hls.js使用hlsJsPlugin配置
      playerOption.hlsJsPlugin = {
        ...playerOption.hls,
        hlsOpts: {
          enableWorker: false,
        },
      }
    }
    player = new (PlayerV3 as any)(playerOption)
  } else {
    console.log('版本错误')
  }
  player.urlInfo = url
  player.index = url.index
  players[i] = player
  props.MultiViewPlayer.players[i] = player

  //禁止右键菜单
  player.video?.addEventListener('contextmenu', (e: any) => {
    e.preventDefault()
  })

  if (i == 0) {
    player.on('canplay', function (p: any) {
      currentTime.value = p.currentTime
      props.MultiViewPlayer.currentTime = p.currentTime
      duration.value = p.duration
      props.MultiViewPlayer.duration = p.duration
      // that.setTime(p);
      // that.updateProgress();
    })
    player.on('timeupdate', function (p: any) {
      currentTime.value = p.currentTime
      props.MultiViewPlayer.currentTime = p.currentTime
      if (duration.value != p.duration) {
        duration.value = p.duration
      }
      props.MultiViewPlayer.emit('timeupdate', props.MultiViewPlayer)
    })
    setTimeout(() => {
      if (
        option.version == 3 &&
        url.path.split('?')[0].includes('.m3u8') &&
        option.isLive &&
        i == 0
      ) {
        //音视频分离解码器
        const demux = new TSDemux({
          debug: false,
        })
        let interval: any
        demux.on(Events.DEMUX_DATA, (e: any) => {
          // console.log(e);
          //time单位（s）
          const time = e.pes.PTS / 90000
          if (time) {
            props.MultiViewPlayer.currentLiveTime = parseInt(String(time))
            //ts文件一片5s，一次5片，故第一帧为25s延迟，则加上25
            props.MultiViewPlayer.currentLiveTime += 25
            demux.destroy()
            if (interval) clearInterval(interval)
            interval = setInterval(() => {
              if (!props.MultiViewPlayer.paused) {
                props.MultiViewPlayer.currentLiveTime += 0.1
                props.MultiViewPlayer.emit('timeupdate', props.MultiViewPlayer)
              }
            }, 100)
          }
          // props.MultiViewPlayer.currentLiveTime = time;
          // props.MultiViewPlayer.emit("timeupdate", props.MultiViewPlayer);
          // let ss = formatSeconds(time);
          // console.log(ss);
        })
        // 当push进来的数据都解析并吐出后，会产生如下事件。用来告诉使用者数据已经解析完毕
        demux.on(Events.DONE, (e: any) => {
          // console.log(e);
          // 数据消耗完毕之后，管道进行了flush动作
        })
        player.plugins?.hlsjsplugin?.hls?.on(
          'hlsFragLoaded',
          function (event: any, data: any) {
            // console.log("data.payload", data.payload);
            demux.push(data.payload, {
              // 本解码器支持推送部分数据
              // 当done设置为true后，如果数据解码完毕没有剩余数据，则认为数据已经推送完毕，Events.DONE才会发出。
              // 当done设置为false后，Events.DONE不会发出，等待后续数据推送
              done: true,
            })
          }
        )
        player.on('pause', () => {
          console.log(player.plugins?.hlsjsplugin?.hls)
          player.plugins?.hlsjsplugin?.hls?.stopLoad()
        })
        player.on('play', () => {
          player.plugins?.hlsjsplugin?.hls?.startLoad()
        })
      }
    })
  }
  player.on('canplay', function (p: any) {
    //多路流取最小的duration
    if (duration.value > p.duration) {
      duration.value = p.duration
    }
  })
  player.on('ended', function (e: any) {
    if (option.isLive) {
      console.warn('播放器' + i + '直播停止')
      // retry(player, i)
    } else {
      console.warn('点播停止')
      seek(0)
      setTimeout(() => {
        pause()
      }, 10)
    }
  })
  player.on('error', function (e: any) {
    console.error(e)
    props.MultiViewPlayer.emit('error', e)
    if (player.config.errorTips) {
      const parser = new DOMParser()
      const doc = parser.parseFromString(player.config.errorTips, 'text/html') // 第2个参数为mimeType，可以为 text/html, text/xml 等等这些
      // 解析好的DOM节点
      if (player.errorDom) {
        player.errorDom.remove()
      }
      player.errorDom = doc.body
      player.errorDom.classList.add('error_tips')
      player.root.appendChild(player.errorDom)
    }
    if (e == 0 || (e && e.errorCode == 5106)) {
      //无画面
      console.log('初始化无画面')
      return
    }
  })
  player.on('play', function (e: any) {
    // console.log(player)
    // console.warn("播放器" + i + "开始播放");
    try {
      play()
    } catch {}
  })
  //绑定弹幕插件
  player.on('ready', function (e: any) {
    if (option.danmu && !danmu.value) {
      danmu.value = new DanmuJs({
        container: danmuRef, //弹幕容器，该容器发生尺寸变化时会自动调整弹幕行为
        containerStyle: {
          //弹幕容器样式
          // zIndex: 100
        },
        player: player.video, //配合音视频元素（video或audio）同步使用时需提供该项
        comments: [
          //弹幕预存数组,配合音视频元素（video或audio）同步使用时需提供该项
          // {
          //   // duration: 20000, //弹幕持续显示时间,毫秒(最低为5000毫秒)
          //   moveV: 100, //弹幕匀速移动速度(单位: px/秒)，设置该项时duration无效
          //   id: '1', //弹幕id，需唯一
          //   start: 3000, //弹幕出现时间（毫秒）
          //   prior: true, //该条弹幕优先显示，默认false
          //   color: true, //该条弹幕为彩色弹幕，默认false
          //   txt: '长弹幕长弹幕长弹幕长弹幕长弹幕', //弹幕文字内容
          //   style: {
          //     //弹幕自定义样式
          //     color: '#fff',
          //     fontSize: '16px',
          //     padding: '2px 11px'
          //   },
          //   // mode: 'top', //显示模式，top顶部居中，bottom底部居中，scroll滚动，默认为scroll
          //   // like: {
          //   //   // 点赞相关参数
          //   //   el: likeDOM, // el 仅支持传入 dom
          //   //   style: {
          //   //     // el 绑定样式
          //   //     paddingLeft: '10px',
          //   //     color: '#ff0000'
          //   //   }
          //   // }
          //   // el: DOM //直接传入一个自定义的DOM元素作为弹幕，使用该项的话会忽略所提供的txt和style
          // }
        ],
        area: {
          //弹幕显示区域
          start: 0, //区域顶部到播放器顶部所占播放器高度的比例
          end: 1, //区域底部到播放器顶部所占播放器高度的比例
          lines: undefined, // 弹幕虚拟轨道显示行数。当指定行数时，显示范围 start/end 不生效；当弹幕字体大小超过所需要的总虚拟轨道数时，弹幕也不会出现在轨道上，因此请设置好弹幕fontSize及影响弹幕高度的其他样式，让弹幕和轨道高度匹配
        },
        channelSize: 40, // 轨道大小
        mouseControl: true, // 打开鼠标控制, 打开后可监听到 bullet_hover 事件。danmu.on('bullet_hover', function (data) {})
        mouseControlPause: false, // 鼠标触摸暂停。mouseControl: true 生效
        //bOffset: 1000, // 可调节弹幕横向间隔（毫秒）
        defaultOff: true, // 开启此项后弹幕不会初始化，默认初始化弹幕
        chaseEffect: true, // 开启滚动弹幕追逐效果, 默认为true
      })
    }
  })
  player.on('fullscreen_change', function (isFullScreen: boolean) {
    //目前只支持xgplayer v3
    if (isFullScreen && textTrackSrcs.value.length) {
      //字幕
      player.textTrack = new XgSubtitle({
        player: player, // 可选, 如果初始化的时候已经有播放器实例
        domRender: true, // 默认为true，会创建dom自行渲染，如果配置为false则只触发更新事件, 不做dom更新
        defaultOpen: true, // 是否默认开启字幕
        mode: 'bg', //可选 字幕显示模式，支持bg(背景）和 stroke(字体边框填充)，默认stroke
        line: 'double', // 可选 字幕最大显示行数 默认单行，single, 支持single/double/three、
        updateMode: 'vod', // 字幕更新类型，vod-字幕内容会做缓存，live-字幕内容不做缓存, 渲染完即丢弃， 默认为vod 1.1.0 之后的版本支持
        renderMode: 'normal', // 渲染方式，step - 逐字渲染 normal或者''- 普通渲染
        debugger: false, // 调试信息输出，默认为false
        subTitles: [
          {
            label: '',
            language: '1', //必选
            id: '1', //必选
            isDefault: true, //必选 是否是默认字幕
            url: textTrackSrcs.value[0], //必选 字幕链接地址
          },
        ],
      })
      player.textTrack.attachPlayer(player)
    } else {
      if (player.textTrack) {
        player.textTrack.destroy()
        player.textTrack = null
      }
    }
    if(isFullScreen){
      playerGroupRef.value.querySelector('#wm_div_id')?.remove()
      initWaterMark(player.root)
    }else{
      player.root.querySelector('#wm_div_id')?.remove()
      initWaterMark(playerGroupRef.value)
    }
  })
}

const destroy = () => {
  players.map((player) => {
    try {
      player.hls && player.hls.destroy(true)
    } catch {}
    try {
      if (player.__flv__) {
        player.__flv__.pause()
        player.__flv__.unload()
        player.__flv__.detachMediaElement()
        player.__flv__.destroy(true)
      }
    } catch {}
    try {
      player?.destroy && player?.destroy(true)
    } catch {}
  })
}

// 播放
const play = () => {
  status.value = 1
  props.MultiViewPlayer.paused = false
  players.map((player: any) => {
    //播放时再判断一次音源
    if (
      player.index ==
      (activeVoice.value == -1 ? sortRule.value[0] : activeVoice.value)
    ) {
      player.muted = false
      player.volume = volume.value
    } else {
      player.muted = true
      player.volume = 0
    }

    if (!option.isLive) {
      player.currentTime = currentTime.value
    }

    if (option.version === 4) {
      try {
        player.play()
      } catch (err) {
        console.warn(err)
      }
      return
    }

    if (option.preload === false) {
      //预加载开关，只播放视窗内或开启了声音的画面，打开以防止显卡性能差的电脑多路流播放卡顿
      if (player.ifShow || !player.muted) {
        try {
          player.play()
        } catch (err) {
          console.warn(err)
        }
        if (option.isLive) {
          //直播重新加载获取最新
          // player.reload() //会报错，注释掉
        }
      } else {
        try {
          player.pause()
        } catch (err) {
          console.warn(err)
        }
      }
    } else {
      if (player.paused) {
        try {
          player.play()
        } catch (err) {
          console.warn(err)
        }
      }
    }
  })
}

// 暂停
const pause = () => {
  status.value = 2
  props.MultiViewPlayer.paused = true
  players.map((player) => {
    try {
      player.pause()
    } catch (err) {
      console.warn(err)
    }
  })
}

const onAdjustChange = (valObj: { name: string; value: number }) => {
  console.log(playerGroupRef.value)
  let val = valObj.value
  let styles = playerGroupRef.value.style
  let filterStr =
    styles.filter ||
    styles.webkitFilter ||
    styles.mozFilter ||
    styles.msFilter ||
    styles.oFilter ||
    ''
  if (valObj.name == 'saturate') {
    //饱和度最大10
    let saturate = 1
    if (val > 0.5) {
      saturate = 1 + (val - 0.5) * 2 * (10 - 1)
    } else {
      saturate = val * 2
    }
    if (filterStr.includes('saturate')) {
      playerGroupRef.value.style.filter = filterStr.replace(
        /saturate\((.*?)\)/,
        'saturate(' + saturate + ')'
      )
    } else {
      playerGroupRef.value.style.filter =
        filterStr + ' saturate(' + saturate + ')'
    }
  } else if (valObj.name == 'brightness') {
    //亮度最大值为20
    let brightness = 1
    if (val > 0.5) {
      brightness = 1 + (val - 0.5) * 2 * (20 - 1)
    } else {
      brightness = val * 2
    }
    if (filterStr.includes('brightness')) {
      playerGroupRef.value.style.filter = filterStr.replace(
        /brightness\((.*?)\)/,
        'brightness(' + brightness + ')'
      )
    } else {
      playerGroupRef.value.style.filter =
        filterStr + ' brightness(' + brightness + ')'
    }
  } else if (valObj.name == 'contrast') {
    //对比度最大值为4
    let contrast = 1
    if (val > 0.5) {
      contrast = 1 + (val - 0.5) * 2 * (4 - 1)
    } else {
      contrast = val * 2
    }
    if (filterStr.includes('contrast')) {
      playerGroupRef.value.style.filter = filterStr.replace(
        /contrast\((.*?)\)/,
        'contrast(' + contrast + ')'
      )
    } else {
      playerGroupRef.value.style.filter =
        filterStr + ' contrast(' + contrast + ')'
    }
  }
}

const onVolumeChange = (valObj: { name: string; value: number }) => {
  setVolume(valObj.value)
}

const setVolume = (val: number) => {
  oldVolume.value = volume.value
  volume.value = parseFloat((val || 0).toFixed(2))

  players.map((player, i) => {
    if (!player.muted) {
      player.volume = volume.value
    } else {
      player.volume = 0
    }
  })
}

const setMuted = (flag: boolean) => {
  if (flag != muted.value) {
    toggleMuted()
  }
}

const toggleMuted = () => {
  if (volume.value != 0) {
    setVolume(0)
    muted.value = true
  } else {
    setVolume(oldVolume.value)
    muted.value = false
  }
}

//切换到主画面
const handleToMain = (index: number) => {
  let sort = [...sortRule.value]
  let cur = sort[index]
  sort[index] = sort[0]
  sort[0] = cur
  sortRule.value = sort
  refreshVoice()
}
//选中某路
const onSelectScreen = (
  url: any,
  playerIndex: number,
  urlIndex: number,
  sortIndex: number
) => {
  sortRule.value = sortRule.value.map((el, i) => {
    if (i == sortIndex) {
      return urlIndex
    } else {
      return el
    }
  })
  playerInit(url, playerIndex)
  refreshVoice()
}

const onScreenSelectChange = (e: any, url: any, index: number) => {
  if (e.target.checked) {
    //选中
    if (sortRule.value.length < 3) {
      //初始化无3路
      sortRule.value = [...sortRule.value, index]
    } else if (sortRule.value.includes(-1)) {
      //去掉-1
      let arr = sortRule.value.filter((el) => el != -1)
      arr.push(index)
      //长度不够补齐-1
      while (arr.length < 3) {
        arr.push(-1)
      }
      sortRule.value = arr
    } else {
      sortRule.value = [
        ...sortRule.value.slice(0, sortRule.value.length - 1),
        index,
      ]
    }
    //重新初始化播放器
    let pIndexs = players.map((el) => el.index)
    let currentPlayerIndex = -1
    if (pIndexs.includes(url.index)) {
      //有已初始化过的该播放器，直接使用
      currentPlayerIndex = pIndexs.indexOf(url.index)
    } else {
      //取第一个空闲的播放器销毁重新初始化
      currentPlayerIndex = pIndexs.findIndex(
        (index) => !sortRule.value.includes(index)
      )
    }
    if (currentPlayerIndex != -1) {
      playerInit(url, currentPlayerIndex)
    } else {
      //播放器已存在，直接显示
    }
  } else {
    //取消选中
    if (sortRule.value.filter((el) => el != -1).length > 1) {
      let arr = sortRule.value.filter((el) => {
        return el != Number(e.target.value)
      })

      while (arr.length < 3) {
        arr.push(-1)
      }
      sortRule.value = arr
    } else {
      //至少选择一路
      e.target.checked = true
    }
  }
  refreshVoice()
}

//切换画面后重新判断音源
const refreshVoice = () => {
  if (!sortRule.value.includes(activeVoice.value)) {
    //关闭了音源画面，重新赋值音源
    activeVoice.value = sortRule.value[0]
  } else if (
    selectedVoice.value == -1 &&
    activeVoice.value != sortRule.value[0]
  ) {
    //联动画面
    activeVoice.value = sortRule.value[0]
  }
}

const onVoiceSelectChange = (e: any, url: any, index: number) => {
  if (e.target.checked) {
    //下拉选中音源
    selectedVoice.value = index
    if (index != -1) {
      //真实播放音源
      activeVoice.value = index
    } else {
      activeVoice.value = sortRule.value[0]
    }
  } else {
    //禁止取消选择
    e.target.checked = true
  }
}

const initFullScreen = () => {
  const doc: any = document
  //监听esc键和F11键退出
  doc.addEventListener('keyup', (e: any) => {
    if ((e.keyCode == 27 || e.keyCode == 122) && ifFullScreen.value) {
      //esc键
      ifFullScreen.value = false
      props.MultiViewPlayer.emit('fullscreen', false)
    }
  })
}

//全屏兼容方法
const docReqFullScreen = (dom: any, flag: boolean = true) => {
  if (!dom) return
  const doc: any = document
  if (flag) {
    //进入全屏
    if (doc.documentElement.RequestFullScreen) {
      dom.RequestFullScreen()
    } else if (doc.documentElement.mozRequestFullScreen) {
      //兼容火狐
      dom.mozRequestFullScreen()
    } else if (doc.documentElement.webkitRequestFullScreen) {
      dom.webkitRequestFullScreen()
    } else if (doc.documentElement.msRequestFullscreen) {
      //兼容IE,只能写msRequestFullscreen
      dom.msRequestFullscreen()
    }
  } else {
    //退出全屏
    if (doc.exitFullScreen) {
      doc.exitFullscreen()
    } else if (doc.mozCancelFullScreen) {
      //兼容火狐
      doc.mozCancelFullScreen()
    } else if (doc.webkitExitFullscreen) {
      //兼容谷歌等
      doc.webkitExitFullscreen()
    } else if (doc.msExitFullscreen) {
      //兼容IE
      doc.msExitFullscreen()
    }
  }
}

//全屏按钮
const toggleFullScreen = () => {
  const doc: any = document
  ifFullScreen.value = !ifFullScreen.value
  props.MultiViewPlayer.emit('fullscreen', ifFullScreen.value)
  docReqFullScreen(containerRef?.value, ifFullScreen.value)
}

//初始化弹幕
const initDanmu = () => {
  danmu.value = new DanmuJs({
    container: danmuRef.value, //弹幕容器，该容器发生尺寸变化时会自动调整弹幕行为
    containerStyle: {
      //弹幕容器样式
      // zIndex: 100
    },
    player: players[0]?.video, //配合音视频元素（video或audio）同步使用时需提供该项
    comments: [
      //弹幕预存数组,配合音视频元素（video或audio）同步使用时需提供该项
      // {
      //   // duration: 20000, //弹幕持续显示时间,毫秒(最低为5000毫秒)
      //   moveV: 100, //弹幕匀速移动速度(单位: px/秒)，设置该项时duration无效
      //   id: '1', //弹幕id，需唯一
      //   start: 3000, //弹幕出现时间（毫秒）
      //   prior: true, //该条弹幕优先显示，默认false
      //   color: true, //该条弹幕为彩色弹幕，默认false
      //   txt: '长弹幕长弹幕长弹幕长弹幕长弹幕', //弹幕文字内容
      //   style: {
      //     //弹幕自定义样式
      //     color: '#fff',
      //     fontSize: '16px',
      //     padding: '2px 11px'
      //   },
      //   // mode: 'top', //显示模式，top顶部居中，bottom底部居中，scroll滚动，默认为scroll
      //   // like: {
      //   //   // 点赞相关参数
      //   //   el: likeDOM, // el 仅支持传入 dom
      //   //   style: {
      //   //     // el 绑定样式
      //   //     paddingLeft: '10px',
      //   //     color: '#ff0000'
      //   //   }
      //   // }
      //   // el: DOM //直接传入一个自定义的DOM元素作为弹幕，使用该项的话会忽略所提供的txt和style
      // }
    ],
    area: {
      //弹幕显示区域
      start: 0, //区域顶部到播放器顶部所占播放器高度的比例
      end: 1, //区域底部到播放器顶部所占播放器高度的比例
      lines: undefined, // 弹幕虚拟轨道显示行数。当指定行数时，显示范围 start/end 不生效；当弹幕字体大小超过所需要的总虚拟轨道数时，弹幕也不会出现在轨道上，因此请设置好弹幕fontSize及影响弹幕高度的其他样式，让弹幕和轨道高度匹配
    },
    channelSize: 40, // 轨道大小
    mouseControl: true, // 打开鼠标控制, 打开后可监听到 bullet_hover 事件。danmu.on('bullet_hover', function (data) {})
    mouseControlPause: false, // 鼠标触摸暂停。mouseControl: true 生效
    //bOffset: 1000, // 可调节弹幕横向间隔（毫秒）
    defaultOff: true, // 开启此项后弹幕不会初始化，默认初始化弹幕
    chaseEffect: true, // 开启滚动弹幕追逐效果, 默认为true
  })
  //赋值给父类
  props.MultiViewPlayer.danmu = danmu.value
}
//开关弹幕
const toggleDanmu = () => {
  danmuOpen.value = !danmuOpen.value
  if (danmuOpen.value) {
    //开启弹幕
    danmu.value?.start()
  } else {
    //关闭弹幕
    danmu.value?.stop()
  }
}

watch(
  () => activeVoice.value,
  () => {
    //监听音源切换，初始化会触发
    setTimeout(() => {
      const trueVoice =
        activeVoice.value == -1 ? sortRule.value[0] : activeVoice.value
      if (props.MultiViewPlayer) {
        props.MultiViewPlayer.voiceUrl = option.urls[trueVoice]
        props.MultiViewPlayer.emit('voiceChange', trueVoice)
      }
      players.map((p: any) => {
        //播放按钮操作会置一次muted，防止初始化音源控制无效，再置回来
        //联动画面选择主画面音源
        if (p.index == trueVoice) {
          p.muted = false
          p.video.muted = false
          p.volume = volume.value
        } else {
          p.muted = true
          p.video.muted = true
          p.volume = 0
        }
      })
    }, 200)
  }
)

//切换速度
const handleSpeedChange = (item: number) => {
  currentSpeed.value = item
  players.map((player) => {
    player.playbackRate = item
  })
}

const seek = (time: number) => {
  currentTime.value = time
  players.map((p) => {
    p.currentTime = time
  })
}

//截屏
const screenshoot = () => {
  const dom = playerGroupRef.value
  const canvas = document.createElement('canvas')
  canvas.width = dom.offsetWidth
  canvas.height = dom.offsetHeight
  let canvasCtx: any = canvas.getContext('2d')
  players.map((player) => {
    let vDom = player.root
    if (vDom.parentNode.style.display == 'none') return
    let vRect = vDom.getBoundingClientRect()
    let cRect = dom.getBoundingClientRect()
    // console.log(player)
    canvasCtx.drawImage(
      player.video,
      vRect.left - cRect.left,
      vRect.top - cRect.top,
      vDom.offsetWidth,
      vDom.offsetHeight
    )
  })
  return canvas
}

//截取某个画面
const screenshootByName = (name: string) => {
  return new Promise((resolve, reject) => {
    let player = players.find((p) => {
      return p.urlInfo.name.includes(name)
    })
    if (!player) reject({ msg: '未找到画面！' })
    let dom = player.root
    if (dom.parentNode.style.display == 'none') reject({ msg: '未开启画面！' })
    const canvas = document.createElement('canvas')
    //限制1920宽度
    canvas.width = player._videoWidth || 1920
    canvas.height =
      player._videoHeight || dom.offsetHeight * (1920 / dom.offsetWidth)
    let canvasCtx: any = canvas.getContext('2d')
    canvasCtx.drawImage(player.video, 0, 0, canvas.width, canvas.height)
    resolve(canvas.toDataURL('image/jpeg'))
    canvas.remove()
  })
}

const handleItemFullScreen = (i: number) => {
  if (option.version == 2) {
    console.log(players[i])
    try {
      let root: any = players[i].root
      if (!root) return
      docReqFullScreen(root)
    } catch {}
  } else {
    players[i].getFullscreen()
  }
}

//切换字幕
const changeTextTrack = (srclang: string | null) => {
  if (srclang && textTrackLangs.value.includes(srclang)) {
    if (textTrackLangs.value.length == 1) {
      //不执行操作
      return
    }
    textTrackLangs.value = textTrackLangs.value.filter((s) => s != srclang)
  } else if (srclang) {
    textTrackLangs.value.push(srclang)
  } else {
    //关闭
    textTrackLangs.value = []
  }
  textTrackRef.value.changeLanguage(textTrackLangs.value)
}
const onChangeLanguage = (langs: string[], srcs: string[]) => {
  textTrackLangs.value = langs
  textTrackSrcs.value = srcs
  props.MultiViewPlayer?.emit('textTrackChange', srcs)
}

//直接显示字幕
const pushTextTrack = (textTrack: any) => {
  textTrackRef.value.pushTextTrack(textTrack)
}

const getSortIndex = (i: number) => {
  const index = sortRule.value.findIndex((el) => el == players[i]?.index)
  return index
}
</script>

<template>
  <div
    :class="`multi_view_player_container display_style_${
      option.display || 1
    } version_${option.version || 3}`"
    :num="sortRule.filter((el) => el != -1).length"
    ref="containerRef"
  >
    <Advertisement
      :currentTime="currentTime"
      :player="props.MultiViewPlayer"
      :list="option.advertisement ?? []"
    />
    <div class="player_group" ref="playerGroupRef">
      <div
        v-for="(n, i) of 3"
        class="player_item"
        :id="'player_' + i"
        :data-sort="getSortIndex(i)"
      >
        <div class="item_tool_bar">
          <div class="screen_select">
            <span class="selected">{{
              urls[sortRule.find((el) => el == players[i]?.index) as any]?.name
            }}</span>
            <div
              class="select_list"
              :style="{ maxHeight: players[i]?.root?.offsetHeight - 30 + 'px' }"
            >
              <div
                v-for="(url, j) in urls"
                :class="{ select_item: true, disabled: sortRule.includes(j) }"
                @click="
                  onSelectScreen(
                    url,
                    i,
                    j,
                    sortRule.findIndex((el) => el == players[i]?.index)
                  )
                "
              >
                {{ url.name }}
              </div>
            </div>
          </div>
          <div class="item_tool_bar_right">
            <Iconfont
              v-if="sortRule.findIndex((el) => el == players[i]?.index) !== 0"
              type="icon-qiehuan"
              @click="
                handleToMain(
                  sortRule.findIndex((el) => el == players[i]?.index)
                )
              "
            />
            <Iconfont
              type="icon-full_screen_icon"
              @click="handleItemFullScreen(i)"
            />
          </div>
        </div>
        <div
          class="video_container"
          :class="['fit_' + (option.fit || '')]"
          :ref="getPlayerRef(i)"
          @click="
            handleToMain(sortRule.findIndex((el) => el == players[i]?.index))
          "
        ></div>
      </div>
    </div>
    <div class="danmu_container" ref="danmuRef"></div>
    <!-- <div v-show="currentTextTrack?.part" class="text_track_content" @mousedown="onTextTrackMove">{{ replaceDot(currentTextTrack.part) }}</div> -->
    <TextTrack
      :option="option"
      :activeVoice="activeVoice == -1 ? sortRule[0] : activeVoice"
      :currentTime="currentTime"
      :sortRule="sortRule"
      :container="containerRef"
      @changeLanguage="onChangeLanguage"
      ref="textTrackRef"
    />
    <div v-if="status != 1" class="center_play_btn" @click="play">
      <Iconfont type="icon-play_icon" />
    </div>
    <div class="tool_bar">
      <!-- 进度条 -->
      <Progress
        ref="progressRef"
        v-show="!option.isLive"
        :currentTime="currentTime"
        :duration="duration"
        :fragmentList="option.fragmentList"
        @emit="(key, value) => props.MultiViewPlayer.emit(key, value)"
        @seek="seek"
      />
      <!-- 播放按钮 -->
      <Iconfont v-if="status != 1" type="icon-play_icon" @click="play" />
      <Iconfont v-else type="icon-pause_icon" @click="pause" />
      <!-- 时间 -->
      <div class="multi_view_player_time">
        <span v-show="!option.isLive"
          >{{ formatSeconds(currentTime ?? 0) }} /
          {{ formatSeconds(duration ?? 0) }}</span
        >
      </div>
      <!-- 弹幕 -->
      <div
        v-show="option.danmu"
        :class="{ danmu_btn: true, active: danmuOpen }"
        @click="toggleDanmu"
      ></div>
      <!-- 字幕 -->
      <div
        class="hover_list text_track"
        v-show="option.textTrack && option.textTrack.length"
      >
        <Iconfont type="icon-a-danmubeifen4" />
        <div class="modal">
          <p
            :class="{ active: !textTrackLangs.length }"
            @click="changeTextTrack(null)"
          >
            关闭
          </p>
          <p
            :class="{ active: textTrackLangs.includes(item.srclang) }"
            v-for="(item, i) in option.textTrack ?? []"
            @click="changeTextTrack(item.srclang)"
          >
            {{ item.label }}
          </p>
        </div>
      </div>
      <!-- 音源 -->
      <div class="hover_list sound_source">
        <span>音源</span>
        <div class="modal">
          <p>
            <input
              type="checkbox"
              id="voice_-1"
              value="-1"
              :checked="selectedVoice == -1"
              @input="(e) => onVoiceSelectChange(e, {}, -1)"
            />
            <label for="voice_-1"> 联动画面 </label>
          </p>
          <p v-for="(item, i) in urls">
            <input
              type="checkbox"
              :id="'voice_' + i"
              :value="i"
              :checked="selectedVoice == i"
              :disabled="!sortRule.includes(i)"
              @input="(e) => onVoiceSelectChange(e, item, i)"
            />
            <label :for="'voice_' + i">
              {{ item.voiceName ?? item.name }}
            </label>
          </p>
        </div>
      </div>
      <!-- 画面 -->
      <div class="hover_list frame">
        <span>画面</span>
        <div class="modal">
          <p v-for="(item, i) in urls">
            <input
              type="checkbox"
              :id="'screen_' + i"
              :value="i"
              :checked="sortRule.includes(i)"
              @input="(e) => onScreenSelectChange(e, item, i)"
            />
            <label :for="'screen_' + i">
              {{ item.name }}
            </label>
          </p>
        </div>
      </div>
      <!-- 速度 -->
      <div v-show="!option.isLive" class="hover_list">
        <span>{{ currentSpeed || 1 }}X</span>
        <div class="modal">
          <p
            :class="{ active: currentSpeed == item }"
            v-for="(item, i) in [0.5, 0.75, 1, 1.5, 2]"
            @click="handleSpeedChange(item)"
          >
            {{ item + 'X' }}
          </p>
        </div>
      </div>
      <!-- 色相饱和度 -->
      <DragBar
        :barArr="[
          {
            name: 'saturate',
            defaultValue: 0.5,
            icon: 'icon-baohedu1',
          },
          {
            name: 'brightness',
            defaultValue: 0.5,
            icon: 'icon-liangdu_o',
          },
          {
            name: 'contrast',
            defaultValue: 0.5,
            icon: 'icon-duibidu',
          },
        ]"
        @change="onAdjustChange"
      >
        <Iconfont type="icon-a-adjusticon" />
      </DragBar>
      <!-- 音量 -->
      <DragBar
        :barArr="[
          {
            name: 'volume',
            defaultValue: volume,
          },
        ]"
        @change="onVolumeChange"
      >
        <Iconfont v-if="muted" type="icon-jingyin" @click="toggleMuted" />
        <Iconfont v-else type="icon-volumn_icon" @click="toggleMuted" />
      </DragBar>
      <!-- 全屏 -->
      <Iconfont
        :type="
          ifFullScreen ? 'icon-exit_full_screen_icon' : 'icon-full_screen_icon'
        "
        @click="toggleFullScreen"
      />
    </div>
  </div>
</template>

<style lang="less">
@import './App.less';
</style>
