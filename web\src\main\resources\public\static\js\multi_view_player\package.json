{"name": "multi_view_player", "version": "0.0.0", "private": true, "type": "module", "scripts": {"start": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build --force"}, "dependencies": {"danmu.js": "^1.1.13", "demuxer": "^2.3.0", "hls.js": "^1.5.17", "less": "^4.2.0", "less-loader": "^12.2.0", "vite-plugin-commonjs": "^0.10.3", "vite-plugin-css": "^1.0.4", "vtt-to-json": "^0.1.1", "vue": "^3.5.12", "xgplayer": "^3.0.21", "xgplayer-flv": "3.0.22", "xgplayer-flv.js": "3.0.10", "xgplayer-hls": "^3.0.21", "xgplayer-hls.js": "^3.0.20", "xgplayer-mpegts.js": "^1.0.1", "xgplayer-subtitles": "^3.0.20"}, "devDependencies": {"@tsconfig/node20": "^20.1.4", "@types/node": "^20.16.11", "@vitejs/plugin-vue": "^5.1.4", "@vue/tsconfig": "^0.5.1", "npm-run-all2": "^6.2.3", "typescript": "~5.5.4", "vite": "^5.4.8", "vue-tsc": "^2.1.6"}}