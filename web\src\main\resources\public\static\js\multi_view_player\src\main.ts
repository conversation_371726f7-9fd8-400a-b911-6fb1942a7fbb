import { createApp } from 'vue'
import App from './App.vue'
import { EventEmitter, canAutoPlay, initGlobalRequestInterceptor } from './utils'

// 初始化全局网络请求拦截器
initGlobalRequestInterceptor()

export interface IAdvertisementItem {
  start: number
  cover: string
  duration: number
}

class MultiViewPlayer {
  name: string
  option: any
  urls: any
  players: any[]
  root: any
  status: number
  volume: any
  currentTime: number
  currentLiveTime: number
  duration: number
  danmu: null
  emit: any
  setOption: any
  /**
   * eventName change-重新布局，fragment-标记，fragmentComfirn-完成标记，voiceChange-音源改变
   */
  on: (eventName: string, listener: any) => void
  off: (eventName: string, listener: any) => void
  version: string
  constructor(option: {
    el: string
    id: string
    urls?: any
    volume?: number
    autoplay?: boolean
    isLive?: boolean
    errorTips?: string
    defaultFrame?: number[]
    defaultAudio?: number[]
    fragmentList?: [number, number][]
    advertisement?: IAdvertisementItem[]
  }) {
    this.name = 'multi_view_player'
    this.option = option
    //最多四个画面
    this.urls = option.urls ? option.urls.slice(0, 4) : []
    this.players = []
    if (option.el) {
      this.root = option.el
    } else {
      this.root = document.getElementById(option.id)
    }
    this.players = []
    this.status = 0 //0-未开始，1-播放中，2-暂停中
    this.volume = option.volume || 1 //音量
    this.currentTime = 0
    this.currentLiveTime = 0
    this.duration = 0
    this.danmu = null //弹幕
    let emiter = new EventEmitter()
    this.emit = emiter.emit.bind(emiter)
    this.on = emiter.on.bind(emiter)
    this.off = emiter.off.bind(emiter)
    this.version = '4.0'
    this.init(option)
  }
  init(option: any) {
    if (option.el) {
      this.root = option.el
    } else {
      this.root = document.getElementById(option.id)
    }
    if (option.autoplay) {
      option.autoplay = canAutoPlay()
    } else {
      option.autoplay = false
    }
    createApp(App, {
      option,
      MultiViewPlayer: this,
    }).mount(this.root)
  }
}

// export default MultiViewPlayer
;(window as any).MultiViewPlayer = MultiViewPlayer

if (import.meta.env.DEV) {
  //测试用代码

  let arr: any[] = [
    {
      name: '回看测试',
      option: {
        watermark_density: 10,
        autoplay: true,
        defaultAudio: [1],
        defaultFrame: [1],
        errorTips: `暂无数据`,
        isLive: true,
        urls: [
          {
            name: '导切画面',
            path: '/bucket-z/unit-cwcc268v239qk92m/video/2024/5/17/A101/3e4c4f9a13e111efb5a3ee71e0fa7294.mp4',
            videoId: '910abe5013e211ef8002621c9b4e6f44',
          },
          {
            name: '导切画面111',
            path: '/bucket-z/unit-cwcc268v239qk92m/video/2024/5/17/A101/3e4c4f9a13e111efb5a3ee71e0fa7294.mp4',
            videoId: '910abe5013e211ef8002621c9b4e6f441',
          },
        ],
        volume: 0.6,
        el: '#player',
        // watermark: '系统管理员',
        // advertisement: [{
        //   cover: '/bucket-k/unit-wz31e60ksb9726wr/picture/2024/11/12/c413c752cc594b06b3eb170d24595d20_1731378532355.jpg',
        //   start: 0,
        //   duration: 5
        // }]
      },
    },
    {
      name: '直播测试',
      option: {
        watermark_density: 5,
        watermark: '系统管理员',
        autoplay: true,
        defaultAudio: [1],
        defaultFrame: [1, 2],
        errorTips: `<div class="no_address">
        <img src="/supervision/static/no_address.2bba04c1.png" alt="">
        <div>暂无信号</div>
      </div>`,
        isLive: true,
        urls: [
          {
            name: '解析流',
            voiceName: '解析流1111',
            path: 'https://live.shanghaitech.edu.cn:443/live/stu_single_333.live.flv',
            error: false,
            hasVoice: false,
          },
          {
            name: '学生单目',
            voiceName: '学生单目111111',
            path: 'https://live.shanghaitech.edu.cn:443/live/2_stu_single_333.live.flv',
            error: false,
            hasVoice: false,
          },
          // {
          //   "name": "屏幕画面",
          //   "voiceName": "屏幕画面11111",
          //   "path": "http://**************:58080/live/tch_pan_23398.live.flv",
          //   "error": false
          // },
          // {
          //   "name": "教师单目1",
          //   "voiceName": "老师单目111",
          //   "path": "http://**************:58080/live/tch_pan_23398.live.flv",
          //   "error": false
          // },
          // {
          //   "name": "学生单目1",
          //   "voiceName": "学生单目1",
          //   "path": "https://zhiliao.sobeylingyun.com:443/live/stu_single_43/hls.m3u8",
          //   "error": false
          // },
          // {
          //   "name": "屏幕画面1",
          //   "voiceName": "屏幕画面1",
          //   "path": "https://zhiliao.sobeylingyun.com:443/live/scr_een_43/hls.m3u8",
          //   "error": false
          // }
        ],
        //直播字幕配置
        textTrack: [
          {
            label: '简体中文',
            srclang: 'ws',
            default: true,
          },
          {
            label: '英文',
            srclang: 'en',
            default: true,
          },
        ],
        volume: 0.6,
        el: '#player',
        version: 4,
        display: 1,
      },
    },
  ]

  let test: any
  arr.map((el: any) => {
    let btn = document.createElement('button')
    btn.innerHTML = el.name
    setTimeout(() => {
      document.body.appendChild(btn)
    }, 2000)
    btn.onclick = function () {
      if (test) {
        test.destroy()
        test = null
      }
      //直播测试
      test = new MultiViewPlayer(el.option)
      // console.log(test)
      test.on('voiceChange', function (e: any) {
        console.log('音源', e)
      })
    }
  })

  test = new MultiViewPlayer(arr[1].option)
  // console.log(test)
  test.on('voiceChange', function (e: any) {
    console.log('音源', e)
  })

  let btn = document.createElement('button')
  btn.innerHTML = '截图'
  btn.onclick = function () {
    let cvs: any = (test as any).screenshoot()
    if (document.getElementsByTagName('canvas').length) {
      document.body.removeChild(document.getElementsByTagName('canvas')[0])
    }
    document.body.appendChild(cvs)
  }

  setTimeout(() => {
    document.body.appendChild(btn)
  }, 2000)
}
