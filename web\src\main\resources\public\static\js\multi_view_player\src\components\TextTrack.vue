<script setup lang="ts">
import { throttle } from "@/utils/index";
import { computed, onMounted, reactive, ref, watch } from "vue";
import { reqVttText } from "@/utils";

const props = defineProps({
  option: {
    type: Object,
    default: () => {},
  },
  currentTime: {
    type: Number,
    default: 0,
  },
  sortRule: {
    type: Array,
    default: () => [],
  },
  //外层容器
  container: {
    type: Object,
    default: () => {},
  },
  //音源索引
  activeVoice: {
    type: Number,
    default: -1,
  },
});

const textTrackList = ref<any>([]);
const currentTextTrack = reactive<any>([]);
const ifMoveTextTrack = ref(false);
const selectedTextLang = ref<string[]>([]);
const timeouts = reactive<any>({});

watch(
  () => props.currentTime,
  (curr) => {
    updateTextTrack(curr);
  }
);

const emit = defineEmits(["changeLanguage"]);

//去除标点符号
const replaceDot = (str: string) => {
  if (!str) return "";
  return (str as any).replaceAll(/，|。/g, "");
};

const onTextTrackMove = (s: any) => {
  ifMoveTextTrack.value = true;
  let textTrackContentDom = s.currentTarget;
  let sx = s.clientX;
  let sy = s.clientY;
  let smx = parseInt(textTrackContentDom.style.marginLeft) || 0;
  let smy = parseInt(textTrackContentDom.style.marginBottom) || 0;
  let startX = parseFloat(textTrackContentDom.style.left || 0);
  props.container.onmousemove = function (m: any) {
    let mx = m.clientX - sx + smx;
    let my = sy - m.clientY + smy;
    my = my < 0 ? 0 : my;
    textTrackContentDom.style.left = startX + mx + "px";
    textTrackContentDom.style.width = `calc(100% - ${startX + mx}px)`;
    textTrackContentDom.style.marginBottom = my + "px";
  };
  document.onmouseup = function (u) {
    ifMoveTextTrack.value = false;
    props.container.onmousemove = null;
  };
};

//切换语言
const changeLanguage = (langArr: string[]) => {
  //置空字幕
  textTrackList.value.length = 0;
  currentTextTrack.length = 0;
  let list: any[] = [];
  langArr
    .sort((prev, next) => {
      let sorts = props.option.textTrack?.map((el: any) => el.srclang) ?? [];
      return sorts.indexOf(prev) - sorts.indexOf(next);
    })
    .map((lang: string) => {
      let cur = props.option.textTrack.find((el: any) => el.srclang == lang);
      if (cur) {
        list.push(cur);
      }
    });
  //获取字幕源
  if (!props.option.isLive && props.activeVoice >= 0) {
    //回看获取字幕
    Promise.all(
      list.map((track) => {
        return reqVttText(track.src[props.activeVoice]);
      })
    ).then((arr) => {
      textTrackList.value = list.map((el, i) => {
        return {
          key: el.srclang,
          list: arr[i],
        };
      });
    });
  }
  const srcs = list.map((track) => {
    return track?.src?.[props.activeVoice];
  });
  selectedTextLang.value = langArr;
  emit("changeLanguage", langArr, srcs);
};

//监听视频播放进度更新字幕
const updateTextTrack = throttle((curr: number) => {
  curr = curr * 1000;
  if (textTrackList.value.length) {
    textTrackList.value.map((item: any, i: number) => {
      let value = item.list.find((part: any) => {
        return part.start <= curr && part.end >= curr;
      });
      if (value && currentTextTrack[i]?.value.part != value.part && !ifMoveTextTrack.value) {
        currentTextTrack[i] = {
          key: item.key,
          value,
        };
      }
    });
  }
}, 100);

//直接展示字幕
const pushTextTrack = (textTrack: { key: string; item: any; timeout: number }) => {
  if (selectedTextLang.value.length) {
    const index = selectedTextLang.value.findIndex((key: string) => key == textTrack.key);
    if (index > -1) {
      currentTextTrack[index] = {
        key: textTrack.key,
        value: textTrack.item,
      };
      if (timeouts[textTrack.key]) {
        clearTimeout(timeouts[textTrack.key]);
        timeouts[textTrack.key] = null;
      }
      timeouts[textTrack.key] = setTimeout(() => {
        currentTextTrack[index] = {};
      }, textTrack.timeout ?? 10 * 1000);
    }
  }
};

//初始化展示默认字幕
watch(
  () => [props.option.textTrack, props.activeVoice],
  () => {
    if (props.option.textTrack && props.option.textTrack.length) {
      const langs = props.option.textTrack.filter((el: any) => el.default).map((el: any) => el.srclang);
      changeLanguage(langs);
    }
  },
  {
    immediate: true,
  }
);

defineExpose({
  changeLanguage,
  pushTextTrack,
});
</script>

<template>
  <div v-show="currentTextTrack.length" class="text_track_content" @mousedown="onTextTrackMove">
    <div class="text_track_item" v-for="item in currentTextTrack" :key="item.key" v-show="item?.value?.part">{{ replaceDot(item?.value?.part) }}</div>
  </div>
</template>

<style lang="less" scoped>
.text_track_content {
  width: 100%;
  max-width: 100%;
  position: absolute;
  left: 0;
  // transform: translate(-50%, 50px);
  bottom: 60px;
  display: -webkit-box;
  text-align: center;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  overflow: hidden;
  padding: 1px 4px;
  word-break: break-word;
  line-height: 1.2;
  color: #fff;
  font-size: 24px;
  cursor: move;
  user-select: none;
  transition: transform 0.5s;
  display: flex;
  flex-direction: column;
  align-items: center;
  .text_track_item {
    background-color: rgba(20, 20, 20, 0.5);
    text-shadow: -1px 1px 0 rgba(0, 0, 0, 0.7), 1px 1px 0 rgba(0, 0, 0, 0.7), 1px -1px 0 rgba(0, 0, 0, 0.7), -1px -1px 0 rgba(0, 0, 0, 0.7);
    & + .text_track_item {
      margin-top: 10px;
    }
  }
}
</style>
