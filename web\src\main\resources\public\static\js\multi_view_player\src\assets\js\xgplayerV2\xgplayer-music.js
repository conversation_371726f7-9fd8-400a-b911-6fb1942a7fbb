window.Music=function(e){var t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)n.d(r,i,function(t){return e[t]}.bind(null,i));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=4)}([function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Analyze=t.Lyric=void 0;var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),i=h(n(2)),o=h(n(11)),a=h(n(12)),s=h(n(14)),l=h(n(34)),c=h(n(35)),u=h(n(36)),f=h(n(37)),p=h(n(38)),d=h(n(39));function h(e){return e&&e.__esModule?e:{default:e}}function y(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var g=void 0,x=15,v=i.default.util,m=function(e){function t(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var n=v.deepCopy({controls:!0,mediaType:"audio",ignores:["fullscreen","start","definition","makeBullet","textTrack","loading","pc","mobile","playbackRate","replay","error","poster"]},e);n.volumeShow||n.ignores.push("volume");var r=y(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,n)),i=r;return i.config.ignores.indexOf("backward")<0&&new l.default(i),i.config.ignores.indexOf("cover")<0&&new c.default(i),i.config.ignores.indexOf("forward")<0&&new u.default(i),i.config.ignores.indexOf("meta")<0&&new f.default(i),i.config.ignores.indexOf("next")<0&&new p.default(i),i.config.ignores.indexOf("prev")<0&&new d.default(i),r.rawConfig=e,r.list="Array"===v.typeOf(n.url)?n.url:[{src:n.url,name:n.name,vid:n.vid,poster:n.poster}],r.name=r.list[0].name,r.vid=r.list[0].vid,r.poster=r.list[0].poster,r.nextIndex=1,r.prevIndex=r.list.length-1,r.halfPass=!1,r.history=[],r.index=0,n.controls?(v.addClass(r.root,"xgplayer-music"),n.width||(r.config.width="100%",r.root.style.width="100%"),n.height||(r.config.height="50px",r.root.style.height="50px"),Object.defineProperty(r,"src",{get:function(){return this.video.currentSrc},set:function(e){var t="String"===v.typeOf(e)?{src:e,name:""}:e;this.history.push(t),this.video.src=t.src},configurable:!0}),r.config.autoplayMuted&&(r.config.volume=r.config.autoplay?0:r.config.volume),r.once("ready",(function(){v.addClass(i.root,"xgplayer-skin-default"),r.config.lang&&"en"===r.config.lang?v.addClass(r.root,"lang-is-en"):"jp"===r.config.lang&&v.addClass(r.root,"lang-is-jp")})),r.once("canplay",(function(){this.config.autoplay&&this.config.autoplayMuted?this.volume=0:this.volume=this.config.volume,this.config.abCycle&&"function"==typeof this.addProgressDot&&(this.addProgressDot(this.config.abCycle.start||0),this.addProgressDot(this.config.abCycle.end||this.duration))})),r.on("timeupdate",(function(){!r.halfPass&&r.currentTime>r.duration/2&&r.confirmOrder(),r.config.abCycle&&(r.currentTime>=(r.config.abCycle.end||r.duration)?(r.config.abCycle.loop||(r.pause(),r.emit("abCycle ended")),r.currentTime=r.config.abCycle.start||0):r.currentTime<(r.config.abCycle.start||0)&&(r.currentTime=r.config.abCycle.start||0))})),r.on("ended",(function(){if(r.config.abCycle)r.config.abCycle.loop&&r.change();else{if("order"===r.mode&&r.index+1>=r.list.length)return r.pause(),void(r.currentTime=0);switch(r.mode){case"sloop":r.change();break;case"order":case"loop":default:r.next()}}})),r.config.segPlay||r.checkOffline(i.list[0].src,i.list[0].vid||i.list[0].name).then((function(e){i.config.url=e,i.start(e)})),r):(r.root.style.display="none",y(r))}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),r(t,[{key:"lyric",value:function(e,t){return this.__lyric__&&this.__lyric__.unbind(this),"Array"!==i.default.util.typeOf(e)&&(e=[].concat(e)),this.__lyric__=new o.default(e,t,this.config.lyricOpts||{}),this.__lyric__.bind(this),this.__lyric__}},{key:"confirmOrder",value:function(){var e=this;this.halfPass=!0,this.nextComput(),this.prevComput(),this.config.preloadNext&&this.checkOffline(this.list[this.nextIndex].src,this.list[this.nextIndex].vid||this.list[this.nextIndex].name).then((function(t){if(t.indexOf("blob:")<0){var n=e.list[e.nextIndex].vid||e.list[e.nextIndex].name;new s.default(e.list[e.nextIndex].src,(function(t){e.database.openDB((function(){e.database.addData(e.database.myDB.ojstore.name,[{vid:n,blob:new Blob([t],{type:'audio/mp4; codecs="mp4a.40.5"'})}]),setTimeout((function(){e.database.closeDB()}),5e3)}))}))}}))}},{key:"nextComput",value:function(){switch(this.mode){case"sloop":case"order":case"loop":this.index+1<this.list.length?this.nextIndex=this.index+1:this.nextIndex=0;break;default:this.nextIndex=Math.ceil(Math.random()*this.list.length)}}},{key:"prevComput",value:function(){switch(this.mode){case"sloop":case"order":case"loop":this.index-1>=0?this.prevIndev=this.index-1:this.prevIndev=this.list.length-1;break;default:this.prevIndev=Math.ceil(Math.random()*this.list.length)}}},{key:"add",value:function(e){this.list.push({src:e.src,name:e.name,vid:e.vid,poster:e.poster})}},{key:"remove",value:function(e){var t=-1;this.list.every((function(n,r){return n.src!==e&&n.name!==e&&n.vid!==e||(t=r,!1)})),t>-1&&this.list.splice(t,1)}},{key:"updateList",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];this.removeAbCycle(),this.pause(),this.currentTime=0,this.list=e,this.nextIndex=0,this.index=0,this.change()}},{key:"change",value:function(){var e=this,t=this,n=t.list[t.index].vid||t.list[t.index].name;this.halfPass=!1,this.checkOffline(t.list[t.index].src,n).then((function(n){if(i.default.m4a)t.video.load(),t.name=t.list[t.index].name,t.vid=t.list[t.index].vid,t.poster=t.list[t.index].poster,t.emit("change",{src:n,name:t.name,vid:t.vid,poster:t.poster});else{if(t.video.pause(),e.config.switchKeepProgress&&!e.ended){var r=t.currentTime;e.once("playing",(function(){t.currentTime=r}))}else t.currentTime=0;t.src=n,t.name=t.list[t.index].name,t.vid=t.list[t.index].vid,t.poster=t.list[t.index].poster,setTimeout((function(){t.video.play().then((function(){t.emit("change",{src:n,name:t.name,vid:t.vid,poster:t.poster})}))}),60)}}))}},{key:"checkOffline",value:function(e,t){var n=this;return new Promise((function(r){n.config.offline||r(e),n.database.openDB((function(){n.database.getDataByKey(n.database.myDB.ojstore.name,t,(function(t){setTimeout((function(){n.database.closeDB()}),5e3),r(t?URL.createObjectURL(t.blob):e)}))}))}))}},{key:"next",value:function(){this.halfPass||(this.halfPass=!0,this.nextComput()),this.index=this.nextIndex,this.change()}},{key:"prev",value:function(){this.halfPass||(this.halfPass=!0,this.prevComput()),this.index=this.prevIndex,this.change()}},{key:"setIndex",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.nextIndex=e,this.index=e,this.change()}},{key:"forward",value:function(){this.currentTime=this.currentTime+x<this.duration?this.currentTime+x:this.duration-.1}},{key:"backward",value:function(){this.currentTime=this.currentTime-x>0?this.currentTime-x:0}},{key:"analyze",value:function(e){return new a.default(this,e)}},{key:"setAbCycle",value:function(e,t,n){this.config.abCycle={start:e||0,end:t||this.duration,loop:n},"function"==typeof this.removeAllProgressDot&&this.removeAllProgressDot(),"function"==typeof this.addProgressDot&&(this.addProgressDot(this.config.abCycle.start),this.addProgressDot(this.config.abCycle.end))}},{key:"removeAbCycle",value:function(){this.config.abCycle=null,"function"==typeof this.removeAllProgressDot&&this.removeAllProgressDot()}},{key:"mode",get:function(){return g||t.ModeType[2]},set:function(e){switch(e){case 0:case 1:case 2:case 3:g=t.ModeType[e]}this.confirmOrder()}},{key:"timeScale",get:function(){return x||15},set:function(e){x=e}}],[{key:"AudioCtx",get:function(){return window.AudioContext||window.webkitAudioContext}},{key:"ModeType",get:function(){return["order","random","loop","sloop"]}}]),t}(i.default);t.default=m,t.Lyric=o.default,t.Analyze=a.default},function(e,t,n){"use strict";var r=n(27)();e.exports=function(e){return e!==r&&null!==e}},function(e,t){e.exports=window.Player},function(e,t,n){"use strict";e.exports=function(e){return null!=e}},function(e,t,n){e.exports=n(5)},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),n(6);var r,i=n(0),o=(r=i)&&r.__esModule?r:{default:r};t.default=o.default,e.exports=t.default},function(e,t,n){var r=n(7);"string"==typeof r&&(r=[[e.i,r,""]]);var i={hmr:!0,transform:void 0,insertInto:void 0};n(9)(r,i);r.locals&&(e.exports=r.locals)},function(e,t,n){(e.exports=n(8)(!1)).push([e.i,'.xgplayer-skin-default.xgplayer-music .xgplayer-controls{display:-webkit-box;display:-ms-flexbox;display:flex;height:50px;cursor:default}.xgplayer-skin-default.xgplayer-music .xgplayer-controls .xgplayer-backward,.xgplayer-skin-default.xgplayer-music .xgplayer-controls .xgplayer-backward-img{-webkit-box-ordinal-group:1;-ms-flex-order:0;order:0;cursor:pointer}.xgplayer-skin-default.xgplayer-music .xgplayer-controls .xgplayer-backward-img:hover,.xgplayer-skin-default.xgplayer-music .xgplayer-controls .xgplayer-backward:hover{opacity:.85}.xgplayer-skin-default.xgplayer-music .xgplayer-controls .xgplayer-prev,.xgplayer-skin-default.xgplayer-music .xgplayer-controls .xgplayer-prev-img{-webkit-box-ordinal-group:2;-ms-flex-order:1;order:1;cursor:pointer}.xgplayer-skin-default.xgplayer-music .xgplayer-controls .xgplayer-prev-img:hover,.xgplayer-skin-default.xgplayer-music .xgplayer-controls .xgplayer-prev:hover{opacity:.85}.xgplayer-skin-default.xgplayer-music .xgplayer-controls .xgplayer-play,.xgplayer-skin-default.xgplayer-music .xgplayer-controls .xgplayer-play-img{-webkit-box-ordinal-group:3;-ms-flex-order:2;order:2;margin-right:-5px}.xgplayer-skin-default.xgplayer-music .xgplayer-controls .xgplayer-play-img .xgplayer-tips,.xgplayer-skin-default.xgplayer-music .xgplayer-controls .xgplayer-play .xgplayer-tips{display:none}.xgplayer-skin-default.xgplayer-music .xgplayer-controls .xgplayer-next,.xgplayer-skin-default.xgplayer-music .xgplayer-controls .xgplayer-next-img{-webkit-box-ordinal-group:4;-ms-flex-order:3;order:3;cursor:pointer}.xgplayer-skin-default.xgplayer-music .xgplayer-controls .xgplayer-next-img:hover,.xgplayer-skin-default.xgplayer-music .xgplayer-controls .xgplayer-next:hover{opacity:.85}.xgplayer-skin-default.xgplayer-music .xgplayer-controls .xgplayer-forward,.xgplayer-skin-default.xgplayer-music .xgplayer-controls .xgplayer-forward-img{-webkit-box-ordinal-group:5;-ms-flex-order:4;order:4;cursor:pointer}.xgplayer-skin-default.xgplayer-music .xgplayer-controls .xgplayer-forward-img:hover,.xgplayer-skin-default.xgplayer-music .xgplayer-controls .xgplayer-forward:hover{opacity:.85}.xgplayer-skin-default.xgplayer-music .xgplayer-controls .xgplayer-volume{-webkit-box-ordinal-group:6;-ms-flex-order:5;order:5;cursor:pointer}.xgplayer-skin-default.xgplayer-music .xgplayer-controls .xgplayer-volume .xgplayer-icon{bottom:-13px}.xgplayer-skin-default.xgplayer-music .xgplayer-controls .xgplayer-cover{position:static;-webkit-box-ordinal-group:7;-ms-flex-order:6;order:6;width:40px;height:40px;text-align:center;vertical-align:middle;position:relative;top:50%;margin-top:-17px}.xgplayer-skin-default.xgplayer-music .xgplayer-controls .xgplayer-cover img{max-width:100%;max-height:100%}.xgplayer-skin-default.xgplayer-music .xgplayer-controls .xgplayer-progress{position:relative;-webkit-box-ordinal-group:8;-ms-flex-order:7;order:7;top:70%;left:20px;margin-top:-11px;-webkit-box-flex:99;-ms-flex:99;flex:99}.xgplayer-skin-default.xgplayer-music .xgplayer-controls .xgplayer-progress .xgplayer-name{position:absolute;left:0;top:-120%;font-size:12px;color:#ddd}.xgplayer-skin-default.xgplayer-music .xgplayer-controls .xgplayer-progress>*{height:3px;margin-top:8.5px}.xgplayer-skin-default.xgplayer-music .xgplayer-controls .xgplayer-progress .xgplayer-progress-played:after{top:-4px;width:10px;height:10px;content:" ";display:block}.xgplayer-skin-default.xgplayer-music .xgplayer-controls .xgplayer-progress .xgplayer-tips{display:none!important}.xgplayer-skin-default.xgplayer-music .xgplayer-controls .xgplayer-progress:focus .xgplayer-progress-btn,.xgplayer-skin-default.xgplayer-music .xgplayer-controls .xgplayer-progress:hover .xgplayer-progress-btn{top:-5px}.xgplayer-skin-default.xgplayer-music .xgplayer-controls .xgplayer-time{-webkit-box-ordinal-group:9;-ms-flex-order:8;order:8;margin-left:30px;line-height:1;position:relative;top:55%}.xgplayer-skin-default.xgplayer-music .xgplayer-controls .xgplayer-placeholder{-webkit-box-ordinal-group:1000;-ms-flex-order:999;order:999;width:0}.xgplayer-skin-default.xgplayer-music .xgplayer-controls .xgplayer-icon{padding-top:5px}.xgplayer-skin-default.xgplayer-music.xgplayer-ended .xgplayer-controls,.xgplayer-skin-default.xgplayer-music.xgplayer-nostart .xgplayer-controls{display:-webkit-box;display:-ms-flexbox;display:flex}.xgplayer-skin-default .xgplayer-lyric-item{display:block;line-height:22px;font-size:14px;color:#000}.xgplayer-skin-default .xgplayer-lyric-item.xgplayer-lyric-item-active{color:#7fffd4}.xgplayer-skin-default .xgplayer-lrcWrap{overflow:auto;height:300px;border:1px solid #ddd;padding:20px}.xgplayer-skin-default .xgplayer-lrcForward{position:absolute;top:20%;left:300px;cursor:pointer;width:0;height:0;border-width:0 10px 10px;border-style:solid;border-color:transparent transparent #333}.xgplayer-skin-default .xgplayer-lrcBack{position:absolute;top:80%;left:300px;cursor:pointer;width:0;height:0;border-width:10px 10px 0;border-style:solid;border-color:#333 transparent transparent}',""])},function(e,t){e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var n=function(e,t){var n=e[1]||"",r=e[3];if(!r)return n;if(t&&"function"==typeof btoa){var i=(a=r,"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(a))))+" */"),o=r.sources.map((function(e){return"/*# sourceURL="+r.sourceRoot+e+" */"}));return[n].concat(o).concat([i]).join("\n")}var a;return[n].join("\n")}(t,e);return t[2]?"@media "+t[2]+"{"+n+"}":n})).join("")},t.i=function(e,n){"string"==typeof e&&(e=[[null,e,""]]);for(var r={},i=0;i<this.length;i++){var o=this[i][0];"number"==typeof o&&(r[o]=!0)}for(i=0;i<e.length;i++){var a=e[i];"number"==typeof a[0]&&r[a[0]]||(n&&!a[2]?a[2]=n:n&&(a[2]="("+a[2]+") and ("+n+")"),t.push(a))}},t}},function(e,t,n){var r,i,o={},a=(r=function(){return window&&document&&document.all&&!window.atob},function(){return void 0===i&&(i=r.apply(this,arguments)),i}),s=function(e){return document.querySelector(e)},l=function(e){var t={};return function(e){if("function"==typeof e)return e();if(void 0===t[e]){var n=s.call(this,e);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(e){n=null}t[e]=n}return t[e]}}(),c=null,u=0,f=[],p=n(10);function d(e,t){for(var n=0;n<e.length;n++){var r=e[n],i=o[r.id];if(i){i.refs++;for(var a=0;a<i.parts.length;a++)i.parts[a](r.parts[a]);for(;a<r.parts.length;a++)i.parts.push(m(r.parts[a],t))}else{var s=[];for(a=0;a<r.parts.length;a++)s.push(m(r.parts[a],t));o[r.id]={id:r.id,refs:1,parts:s}}}}function h(e,t){for(var n=[],r={},i=0;i<e.length;i++){var o=e[i],a=t.base?o[0]+t.base:o[0],s={css:o[1],media:o[2],sourceMap:o[3]};r[a]?r[a].parts.push(s):n.push(r[a]={id:a,parts:[s]})}return n}function y(e,t){var n=l(e.insertInto);if(!n)throw new Error("Couldn't find a style target. This probably means that the value for the 'insertInto' parameter is invalid.");var r=f[f.length-1];if("top"===e.insertAt)r?r.nextSibling?n.insertBefore(t,r.nextSibling):n.appendChild(t):n.insertBefore(t,n.firstChild),f.push(t);else if("bottom"===e.insertAt)n.appendChild(t);else{if("object"!=typeof e.insertAt||!e.insertAt.before)throw new Error("[Style Loader]\n\n Invalid value for parameter 'insertAt' ('options.insertAt') found.\n Must be 'top', 'bottom', or Object.\n (https://github.com/webpack-contrib/style-loader#insertat)\n");var i=l(e.insertInto+" "+e.insertAt.before);n.insertBefore(t,i)}}function g(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e);var t=f.indexOf(e);t>=0&&f.splice(t,1)}function x(e){var t=document.createElement("style");return void 0===e.attrs.type&&(e.attrs.type="text/css"),v(t,e.attrs),y(e,t),t}function v(e,t){Object.keys(t).forEach((function(n){e.setAttribute(n,t[n])}))}function m(e,t){var n,r,i,o;if(t.transform&&e.css){if(!(o=t.transform(e.css)))return function(){};e.css=o}if(t.singleton){var a=u++;n=c||(c=x(t)),r=w.bind(null,n,a,!1),i=w.bind(null,n,a,!0)}else e.sourceMap&&"function"==typeof URL&&"function"==typeof URL.createObjectURL&&"function"==typeof URL.revokeObjectURL&&"function"==typeof Blob&&"function"==typeof btoa?(n=function(e){var t=document.createElement("link");return void 0===e.attrs.type&&(e.attrs.type="text/css"),e.attrs.rel="stylesheet",v(t,e.attrs),y(e,t),t}(t),r=C.bind(null,n,t),i=function(){g(n),n.href&&URL.revokeObjectURL(n.href)}):(n=x(t),r=k.bind(null,n),i=function(){g(n)});return r(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;r(e=t)}else i()}}e.exports=function(e,t){if("undefined"!=typeof DEBUG&&DEBUG&&"object"!=typeof document)throw new Error("The style-loader cannot be used in a non-browser environment");(t=t||{}).attrs="object"==typeof t.attrs?t.attrs:{},t.singleton||"boolean"==typeof t.singleton||(t.singleton=a()),t.insertInto||(t.insertInto="head"),t.insertAt||(t.insertAt="bottom");var n=h(e,t);return d(n,t),function(e){for(var r=[],i=0;i<n.length;i++){var a=n[i];(s=o[a.id]).refs--,r.push(s)}e&&d(h(e,t),t);for(i=0;i<r.length;i++){var s;if(0===(s=r[i]).refs){for(var l=0;l<s.parts.length;l++)s.parts[l]();delete o[s.id]}}}};var _,b=(_=[],function(e,t){return _[e]=t,_.filter(Boolean).join("\n")});function w(e,t,n,r){var i=n?"":r.css;if(e.styleSheet)e.styleSheet.cssText=b(t,i);else{var o=document.createTextNode(i),a=e.childNodes;a[t]&&e.removeChild(a[t]),a.length?e.insertBefore(o,a[t]):e.appendChild(o)}}function k(e,t){var n=t.css,r=t.media;if(r&&e.setAttribute("media",r),e.styleSheet)e.styleSheet.cssText=n;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(n))}}function C(e,t,n){var r=n.css,i=n.sourceMap,o=void 0===t.convertToAbsoluteUrls&&i;(t.convertToAbsoluteUrls||o)&&(r=p(r)),i&&(r+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(i))))+" */");var a=new Blob([r],{type:"text/css"}),s=e.href;e.href=URL.createObjectURL(a),s&&URL.revokeObjectURL(s)}},function(e,t){e.exports=function(e){var t="undefined"!=typeof window&&window.location;if(!t)throw new Error("fixUrls requires window.location");if(!e||"string"!=typeof e)return e;var n=t.protocol+"//"+t.host,r=n+t.pathname.replace(/\/[^\/]*$/,"/");return e.replace(/url\s*\(((?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)\)/gi,(function(e,t){var i,o=t.trim().replace(/^"(.*)"$/,(function(e,t){return t})).replace(/^'(.*)'$/,(function(e,t){return t}));return/^(#|data:|http:\/\/|https:\/\/|file:\/\/\/|\s*$)/i.test(o)?e:(i=0===o.indexOf("//")?o:0===o.indexOf("/")?n+o:r+o.replace(/^\.\//,""),"url("+JSON.stringify(i)+")")}))}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.LyricTime=void 0;var r,i=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),o=n(2),a=(r=o)&&r.__esModule?r:{default:r};function s(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var l=function e(t){s(this,e),this.regRule=/(\d{2}(?=:)):(\d{2}(?=\.))\.(\d{2,3})/g,this.regRule.test(t)?this.time=60*RegExp.$1+1*RegExp.$2+1*("0."+RegExp.$3):this.time=-1};t.LyricTime=l;var c=function(){function e(t,n,r){var i=this;s(this,e),this.rawTxts=t,this.txts=t.map((function(e){return e.replace(/^[\r\n]|[\r\n]$/g,"").match(/(\[.*\])[^[]+/g)})),this.isDynamics=t.map((function(e,t){return[].concat(e.match(/\[\d{2}:\d{2}\.\d{2,3}\]/g)).length>0&&i.txts[t].length===i.txts[0].length&&i.txts[t].length>1})),this.isDynamic=this.isDynamics.some((function(e){return e})),this.__ainimateInterval__=0,this.__offset__=0,this.__offsetScale__=.5,this.dom=n,this.lists=[],this.isDynamics.map((function(e,t){e&&i.lists.push(i.txts[t].map((function(e,t){var n=/(\[[\d:\S]+\])([^[]+)/g.test(e),r=RegExp.$1,i=RegExp.$2;return{time:n?new l(r).time:-1,lyric:i,idx:t}})))})),this.list=this.lists.reduce((function(e,t){return e.map((function(e,n){return{time:e.time,lyric:"\n"===e.lyric?""+e.lyric+t[n].lyric:e.lyric+"<br/>"+t[n].lyric,idx:n}}))})).filter((function(e){return!r.removeBlankLine||"\r\n"!==e.lyric&&"\n"!==e.lyric&&""!==e.lyric})),this.line=0}return i(e,[{key:"adjust",value:function(){for(var e,t,n=this.list,r=0,i=n.length;r<i;r++){for(e=r+1;e<i&&!(n[e].time>n[r].time);e++);if(e<i){var o=(n[e].time-n[r].time)/(e-r);for(t=r+1;t<e;t++)n[t].time=n[t-1].time+o}}}},{key:"find",value:function(e){var t=this.list,n=this.__ainimateInterval__,r=this.__offset__;return e=e+r>0?e+r:0,t.filter((function(r,i){var o=r.time,a=i+1;return e>=o&&(t[a]&&1*e+1*n<=t[a].time||a>=t.length)}))}},{key:"bind",value:function(e){var t=this,n=this;return this.__player__=e,!!n.isDynamic&&(n.__handle__=function(){var n=t.find(e.currentTime)[0];n&&n.idx!==t.line&&(t.line=n.idx,e.emit("lyricUpdate",n))}.bind(n,e),e.on("timeupdate",n.__handle__),n.__startHandle__=function(){e.emit("lyricUpdate",n.list[0])}.bind(n,e),e.once("playing",n.__startHandle__),!0)}},{key:"unbind",value:function(e){delete this.__player__,this.__handle__&&(e.off("lyricUpdate",this.__handle__),delete this.__handle__)}},{key:"show",value:function(){var e=this,t=this.dom,n=[],r=this,i=["click","touchstart"];if(t&&1===t.nodeType){var o=a.default.util.createDom("div","<div></div>",{},"xgplayer-lrcWrap");t.appendChild(o),this.list.forEach((function(e){n.push('<xg-lyric-item class="xgplayer-lyric-item" data-idx="'+e.idx+'">'+e.lyric.replace(/[\r\n]/g,"")+"</xg-lyric-item>")})),o.innerHTML=n.join("");var s=a.default.util.createDom("xg-lrcForward","<div></div>",{},"xgplayer-lrcForward");t.appendChild(s),i.forEach((function(e){s.addEventListener(e,(function(e){e.preventDefault(),e.stopPropagation(),r.offset-=r.offsetScale,console.log("lyric go forward "+r.offsetScale+"s")}),!1)}));var l=a.default.util.createDom("xg-lrcBack","<div></div>",{},"xgplayer-lrcBack");t.appendChild(l),i.forEach((function(e){l.addEventListener(e,(function(e){e.preventDefault(),e.stopPropagation(),r.offset+=r.offsetScale,console.log("lyric go back "+r.offsetScale+"s")}),!1)})),this.__updateHandle__=function(t){var n=e.dom.querySelector(".xgplayer-lrcWrap"),r=n.querySelector(".xgplayer-lyric-item-active"),i=n.offsetHeight,o=void 0;r&&(r.className="xgplayer-lyric-item"),(r=n.querySelector('.xgplayer-lyric-item[data-idx="'+t.idx+'"]'))&&(r.className="xgplayer-lyric-item xgplayer-lyric-item-active",(o=r.getBoundingClientRect().top-n.getBoundingClientRect().top+n.scrollTop-i/2)&&(n.scrollTop=o))},this.__player__.on("lyricUpdate",this.__updateHandle__)}else this.__player__.emit("error","lyric container can not be empty")}},{key:"hide",value:function(){this.__updateHandle__.off("lyricUpdate",this.__updateHandle__)}},{key:"interval",set:function(e){this.__ainimateInterval__=e},get:function(){return this.__ainimateInterval__}},{key:"offset",set:function(e){this.__offset__=e},get:function(){return this.__offset__}},{key:"offsetScale",set:function(e){this.__offsetScale__=e},get:function(){return this.__offsetScale__}}]),e}();t.default=c},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),i=n(13);var o=function(){function e(t,n){var r=this;if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.canvas=n,this.player=t,e.AudioCtx){var o=new e.AudioCtx,a=o.createAnalyser(),s=o.createGain();a.minDecibels=-90,a.maxDecibels=-10,a.smoothingTimeConstant=.85,s.gain.setValueAtTime(t.volume,t.currentTime),this.er=o.createMediaElementSource(t.video),this.analyser=a,this.ctx=n.getContext("2d"),this.er.connect(a),a.connect(s),s.connect(o.destination),this.style={bgColor:"#c8c8c8",color:"#643232"},this.__type__="bars",this.__size__=256,this.__status__={switch:"on"},["play","playing","seeked"].forEach((function(e){t.on(e,(function(){r["__"+r.__type__+"__"]()}))})),["seeking","waiting","pause","ended"].forEach((function(e){(0,i.cancelAnimationFrame)(r.__status__[r.__type__])})),t.on("volumechange",(function(){s.gain.setValueAtTime(t.volume,t.currentTime)})),t.on("destroy",(function(){o.close()}))}}return r(e,[{key:"__wave__",value:function(){var e=this;if((0,i.cancelAnimationFrame)(this.__status__.wave),(0,i.cancelAnimationFrame)(this.__status__.bars),"off"!==this.__status__.switch){var t=this.analyser,n=this.canvas,r=this.ctx,o=t.frequencyBinCount,a=new Uint8Array(o),s=n.width,l=n.height,c=new i.Color(this.style.color).toRGB(),u=new i.Color(this.style.color).toRGB();t.fftSize=this.__size__;!function f(){e.__status__.wave=(0,i.requestAnimationFrame)(f),t.getByteTimeDomainData(a),r.clearRect(0,0,s,l),r.fillStyle=u,r.lineWidth=2,r.strokeStyle=c,r.beginPath();for(var p=1*s/o,d=0,h=0;h<o;h++){var y=a[h]/128*l/2;0===h?r.moveTo(d,y):r.lineTo(d,y),d+=p}r.lineTo(n.width,n.height/2),r.stroke()}()}}},{key:"__bars__",value:function(){var e=this;if((0,i.cancelAnimationFrame)(this.__status__.wave),(0,i.cancelAnimationFrame)(this.__status__.bars),"off"!==this.__status__.switch){var t=this.analyser,n=this.canvas,r=this.ctx,o=t.frequencyBinCount,a=new Uint8Array(o),s=n.width,l=n.height,c=new i.Color(this.style.color).toArray(),u=new i.Color(this.style.color).toRGB();t.fftSize=this.__size__;!function n(){e.__status__.bars=(0,i.requestAnimationFrame)(n),t.getByteFrequencyData(a),r.clearRect(0,0,s,l),r.fillStyle=u,r.fillRect(0,0,s,l);for(var f=s/o*2.5,p=void 0,d=0,h=0;h<o;h++)p=a[h],r.fillStyle="rgb("+(p+c[0])+","+c[1]+","+c[2]+")",r.fillRect(d,l-p/2,f,p/2),d+=f+1}()}}},{key:"on",value:function(){this.__status__.switch="on",this["__"+this.__type__+"__"]()}},{key:"off",value:function(){this.__status__.switch="off",(0,i.cancelAnimationFrame)(this.__status__.wave),(0,i.cancelAnimationFrame)(this.__status__.bars)}},{key:"mode",set:function(t){e.Mode.filter((function(e){return e===t})).length&&(this.__type__=t,"on"===this.__status__.switch&&this["__"+t+"__"]())},get:function(){return this.__type__}},{key:"size",set:function(e){e<65536&&(0,i.isSqrt)(e,2)&&(this.__size__=e,this.analyser.fftSize=e,this["__"+this.__type__+"__"]())},get:function(){return this.__size__}},{key:"status",get:function(){return this.__status__.switch}}],[{key:"AudioCtx",get:function(){return window.AudioContext||window.webkitAudioContext}},{key:"Mode",get:function(){return["wave","bars"]}}]),e}();t.default=o,e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}();function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}for(var o,a=0,s="webkit moz ms o".split(" "),l=window.requestAnimationFrame,c=window.cancelAnimationFrame,u=0;u<s.length&&(!l||!c);u++)o=s[u],l=l||window[o+"RequestAnimationFrame"],c=c||window[o+"CancelAnimationFrame"]||window[o+"CancelRequestAnimationFrame"];l&&c||(l=function(e,t){var n=(new Date).getTime(),r=Math.max(0,16-(n-a)),i=window.setTimeout((function(){e.call(n+r)}),r);return a=n+r,i},c=function(e){window.clearTimeout(e)});var f=function(){function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;i(this,e),this.color=e.Valid.test(t)?t:"#ffffff",this.opacity=n}return r(e,[{key:"toArray",value:function(){var e=this.color.slice(1),t=[];return 6===e.length&&(t=(e=e.match(/\d{2}/g)).map((function(e){return Number("0x"+e)}))),t}},{key:"toRGB",value:function(){var e=this.toArray();return 3===e.length?"rgb("+e[0]+","+e[1]+","+e[2]+")":""}},{key:"toGRBA",value:function(){var e=this.toArray();return 3===e.length?"rgba("+e[0]+","+e[1]+","+e[2]+","+this.opacity+")":""}}],[{key:"Valid",get:function(){return/^#[0-9A-F]{6}$|^#[0-9A-F]{3}$/i}}]),e}();t.default={requestAnimationFrame:l,cancelAnimationFrame:c,isSqrt:function(e,t){if(1!==e){for(;1!==e;){if(e%t!=0)return!1;e/=t}return!0}return!0},Color:f},e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r,i=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),o=n(15),a=(r=o)&&r.__esModule?r:{default:r};var s=function(){function e(t,n){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),(0,a.default)(this),this.url=t;var r=new window.XMLHttpRequest;r.target=this,r.responseType="arraybuffer",r.open("get",t),r.onload=function(){200!==r.status&&206!==r.status||n&&n instanceof Function&&n(r.response)},r.onerror=function(e){r.target.emit("error"+e.message)},this.xhr=r,this.run()}return i(e,[{key:"cancel",value:function(){this.xhr.abort()}},{key:"run",value:function(){1===this.xhr.readyState&&this.xhr.send()}}]),e}();t.default=s,e.exports=t.default},function(e,t,n){"use strict";var r,i,o,a,s,l,c,u=n(16),f=n(33),p=Function.prototype.apply,d=Function.prototype.call,h=Object.create,y=Object.defineProperty,g=Object.defineProperties,x=Object.prototype.hasOwnProperty,v={configurable:!0,enumerable:!1,writable:!0};i=function(e,t){var n,i;return f(t),i=this,r.call(this,e,n=function(){o.call(i,e,n),p.call(t,this,arguments)}),n.__eeOnceListener__=t,this},s={on:r=function(e,t){var n;return f(t),x.call(this,"__ee__")?n=this.__ee__:(n=v.value=h(null),y(this,"__ee__",v),v.value=null),n[e]?"object"==typeof n[e]?n[e].push(t):n[e]=[n[e],t]:n[e]=t,this},once:i,off:o=function(e,t){var n,r,i,o;if(f(t),!x.call(this,"__ee__"))return this;if(!(n=this.__ee__)[e])return this;if("object"==typeof(r=n[e]))for(o=0;i=r[o];++o)i!==t&&i.__eeOnceListener__!==t||(2===r.length?n[e]=r[o?0:1]:r.splice(o,1));else r!==t&&r.__eeOnceListener__!==t||delete n[e];return this},emit:a=function(e){var t,n,r,i,o;if(x.call(this,"__ee__")&&(i=this.__ee__[e]))if("object"==typeof i){for(n=arguments.length,o=new Array(n-1),t=1;t<n;++t)o[t-1]=arguments[t];for(i=i.slice(),t=0;r=i[t];++t)p.call(r,this,o)}else switch(arguments.length){case 1:d.call(i,this);break;case 2:d.call(i,this,arguments[1]);break;case 3:d.call(i,this,arguments[1],arguments[2]);break;default:for(n=arguments.length,o=new Array(n-1),t=1;t<n;++t)o[t-1]=arguments[t];p.call(i,this,o)}}},l={on:u(r),once:u(i),off:u(o),emit:u(a)},c=g({},l),e.exports=t=function(e){return null==e?h(c):g(Object(e),l)},t.methods=s},function(e,t,n){"use strict";var r=n(3),i=n(17),o=n(21),a=n(29),s=n(30);(e.exports=function(e,t){var n,i,l,c,u;return arguments.length<2||"string"!=typeof e?(c=t,t=e,e=null):c=arguments[2],r(e)?(n=s.call(e,"c"),i=s.call(e,"e"),l=s.call(e,"w")):(n=l=!0,i=!1),u={value:t,configurable:n,enumerable:i,writable:l},c?o(a(c),u):u}).gs=function(e,t,n){var l,c,u,f;return"string"!=typeof e?(u=n,n=t,t=e,e=null):u=arguments[3],r(t)?i(t)?r(n)?i(n)||(u=n,n=void 0):n=void 0:(u=t,t=n=void 0):t=void 0,r(e)?(l=s.call(e,"c"),c=s.call(e,"e")):(l=!0,c=!1),f={get:t,set:n,configurable:l,enumerable:c},u?o(a(u),f):f}},function(e,t,n){"use strict";var r=n(18),i=/^\s*class[\s{/}]/,o=Function.prototype.toString;e.exports=function(e){return!!r(e)&&!i.test(o.call(e))}},function(e,t,n){"use strict";var r=n(19);e.exports=function(e){if("function"!=typeof e)return!1;if(!hasOwnProperty.call(e,"length"))return!1;try{if("number"!=typeof e.length)return!1;if("function"!=typeof e.call)return!1;if("function"!=typeof e.apply)return!1}catch(e){return!1}return!r(e)}},function(e,t,n){"use strict";var r=n(20);e.exports=function(e){if(!r(e))return!1;try{return!!e.constructor&&e.constructor.prototype===e}catch(e){return!1}}},function(e,t,n){"use strict";var r=n(3),i={object:!0,function:!0,undefined:!0};e.exports=function(e){return!!r(e)&&hasOwnProperty.call(i,typeof e)}},function(e,t,n){"use strict";e.exports=n(22)()?Object.assign:n(23)},function(e,t,n){"use strict";e.exports=function(){var e,t=Object.assign;return"function"==typeof t&&(t(e={foo:"raz"},{bar:"dwa"},{trzy:"trzy"}),e.foo+e.bar+e.trzy==="razdwatrzy")}},function(e,t,n){"use strict";var r=n(24),i=n(28),o=Math.max;e.exports=function(e,t){var n,a,s,l=o(arguments.length,2);for(e=Object(i(e)),s=function(r){try{e[r]=t[r]}catch(e){n||(n=e)}},a=1;a<l;++a)t=arguments[a],r(t).forEach(s);if(void 0!==n)throw n;return e}},function(e,t,n){"use strict";e.exports=n(25)()?Object.keys:n(26)},function(e,t,n){"use strict";e.exports=function(){try{return Object.keys("primitive"),!0}catch(e){return!1}}},function(e,t,n){"use strict";var r=n(1),i=Object.keys;e.exports=function(e){return i(r(e)?Object(e):e)}},function(e,t,n){"use strict";e.exports=function(){}},function(e,t,n){"use strict";var r=n(1);e.exports=function(e){if(!r(e))throw new TypeError("Cannot use null or undefined");return e}},function(e,t,n){"use strict";var r=n(1),i=Array.prototype.forEach,o=Object.create,a=function(e,t){var n;for(n in e)t[n]=e[n]};e.exports=function(e){var t=o(null);return i.call(arguments,(function(e){r(e)&&a(Object(e),t)})),t}},function(e,t,n){"use strict";e.exports=n(31)()?String.prototype.contains:n(32)},function(e,t,n){"use strict";var r="razdwatrzy";e.exports=function(){return"function"==typeof r.contains&&(!0===r.contains("dwa")&&!1===r.contains("foo"))}},function(e,t,n){"use strict";var r=String.prototype.indexOf;e.exports=function(e){return r.call(this,e,arguments[1])>-1}},function(e,t,n){"use strict";e.exports=function(e){if("function"!=typeof e)throw new TypeError(e+" is not a function");return e}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r,i=n(0),o=(r=i)&&r.__esModule?r:{default:r};t.default=function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e);var n=o.default.util,r=t.controls,i=t.config.backwardBtn?t.config.backwardBtn:{},a=void 0;a="img"===i.type?o.default.util.createImgBtn("backward",i.url,i.width,i.height):n.createDom("xg-backward",'<xg-icon class="xgplayer-icon"><svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40">\n                <path transform = "scale(1.5 1.5) translate(8 4.5)"\n                d="m 14,2.99996 0,10 -7,-5 7,-5 z m -7,5 0,5 -7,-5 7,-5 0,5 z m -7,0 0,0 z"></path>\n            </svg></xg-icon>',{},"xgplayer-backward"),r.appendChild(a),["click","touchstart"].forEach((function(e){a.addEventListener(e,(function(e){e.preventDefault(),e.stopPropagation(),t.backward()}),!1)}))},e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r,i=n(0),o=(r=i)&&r.__esModule?r:{default:r};t.default=function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e);var n=o.default.util,r=t.controls,i=n.createDom("xg-cover",'<img src="'+(t.config.poster||t.config.url[0].poster)+'">',{},"xgplayer-cover");r.appendChild(i),t.on("change",(function(e){i.innerHTML='<img src="'+e.poster+'">'}))},e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r,i=n(0),o=(r=i)&&r.__esModule?r:{default:r};t.default=function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e);var n=o.default.util,r=t.controls,i=t.config.forwardBtn?t.config.forwardBtn:{},a=void 0;a="img"===i.type?o.default.util.createImgBtn("forward",i.url,i.width,i.height):n.createDom("xg-forward",'<xg-icon class="xgplayer-icon"><svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40">\n                <path transform = "scale(1.5 1.5) translate(-2 4.5)"\n                d="m 2,2.99996 0,10 7,-5 -7,-5 z m 7,5 0,5 7,-5 -7,-5 0,5 z m 7,0 0,0 z"></path>\n            </svg></xg-icon>',{},"xgplayer-forward"),r.appendChild(a),["click","touchstart"].forEach((function(e){a.addEventListener(e,(function(e){e.preventDefault(),e.stopPropagation(),t.forward()}),!1)}))},e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r,i=n(0),o=(r=i)&&r.__esModule?r:{default:r};t.default=function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e);var n=o.default.util,r=t.controls.querySelector(".xgplayer-progress"),i=n.createDom("xg-name",""+(t.config.name||t.config.url[0].name),{},"xgplayer-name");r.appendChild(i),t.on("change",(function(e){i.innerHTML=""+e.name}))},e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r,i=n(0),o=(r=i)&&r.__esModule?r:{default:r};t.default=function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e);var n=o.default.util,r=t.controls,i=t.config.nextBtn?t.config.nextBtn:{},a=void 0;a="img"===i.type?o.default.util.createImgBtn("next",i.url,i.width,i.height):n.createDom("xg-next",'<xg-icon class="xgplayer-icon"><svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40">\n                <path transform="scale(0.025 0.025)"\n                d="M800 380v768h-128v-352l-320 320v-704l320 320v-352z"></path>\n            </svg></xg-icon>',{},"xgplayer-next"),r.appendChild(a),["click","touchstart"].forEach((function(e){a.addEventListener(e,(function(e){e.preventDefault(),e.stopPropagation(),t.next()}),!1)}))},e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r,i=n(0),o=(r=i)&&r.__esModule?r:{default:r};t.default=function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e);var n=o.default.util,r=t.controls,i=t.config.prevBtn?t.config.prevBtn:{},a=void 0;a="img"===i.type?o.default.util.createImgBtn("prev",i.url,i.width,i.height):n.createDom("xg-prev",'<xg-icon class="xgplayer-icon"><svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40">\n                <path transform = "scale(0.025 0.025)"\n                d="M600 1140v-768h128v352l320-320v704l-320-320v352zz"></path>\n            </svg></xg-icon>',{},"xgplayer-prev"),r.appendChild(a),["click","touchstart"].forEach((function(e){a.addEventListener(e,(function(e){e.preventDefault(),e.stopPropagation(),t.prev()}),!1)}))},e.exports=t.default}]);